'use-strict';

var fs = require('fs');
const path = require('path');
var ConfigReader = require('./utils/configReader').ConfigReader;
var os = require('os');
const { isNotUndefined } = require("./typeSanity");

var env = process.argv[2];
const basePath = process.env.CODEBASEPATH;
const vm = process.env.VM;
const vmPath = vm === 'mac' ? basePath : require('path').join(__dirname, "/");

exports.ROOT_PATH = (env && env == "Production" && (vm !== 'k8s' && vm !== 'mac'))? "/home/<USER>/SeleniumHub/current/" : vmPath;

var configFile = new ConfigReader(exports.ROOT_PATH + "default-conf.json");
configFile = (process.env["HUB_ENV"] == "testing" ? configFile.merge(exports.ROOT_PATH + "conf.test.json") : configFile.merge(exports.ROOT_PATH + "conf.json")).getConfig();

exports.global_registry = {};
exports.user_queue_registry = {};
exports.timeout_registry = {};
exports.globalS3ClientRegistry = {};
exports.sessions_registry = {};
exports.stop_sessions_registry = {};
exports.execution_time_registry = {};
exports.global_ws_registry = {};

// Structure:
// hoothootkey: { genre1: val1, genre2: val2 }
exports.pushToHootHootRegistry = {};
exports.hubProcessingRegistry = {
  'hubRequestCount': 0,
  'hubProcessingTime': 0,
  'userToNginxTime': 0,
  'nginxToHubTime': 0,
  'jarTime': 0
};

// ms to wait before closing the workers so that task from request being served currently are done. (More then so timeout)
exports.workerCloseDelay = 255000;

exports.kafkaWorkerFlags = {
  enabled: false, // enable for all
  partiallyEnabled: false, // enable based on user keys
  batchSize: 100,
  // "grp_2_console_logs" : true - to enable console logs for group 2
  // "user_2_console_logs": true - to enable console logs for user 2
  // "console_logs": true - to enable console logs for all
};

exports.workerConf = {
  kafkaPush: {
    workerFile: 'workers/kafkaPush.js',
    numberWorkers: 0
  }
};

exports.hubLogsDir = configFile["hub_log_dir"] || '/ebs';
exports.eliminateRproxyPrivateHubSubRegions = configFile["eliminate_rproxy_private_hub_sub_regions"] || false;

exports.useBlockedAt = configFile["use-blocked-at-for-eventloop-stacktrace"] || false;

exports.mobileHubTerminalKeepAlive = 0;

// Will use miltiple client based on if its a multiple cluster setup region.
exports.multiRedisClusterSetup = configFile["multi_redis_cluster_setup"] || false;

// This flag decides if certain keys will pe published via pubSub to other hub nodes or to instrumentation service via kafka or both
// It also decides from where certain fields will be pushed to zombies
// 2 = regular pubSub, 1 = send to both pubSub and Instrumentation service (push to zombies from hub), 0 = only send to Instrumentation Service and push fields=Y in pubSubKafkaKeys from service
exports.instrumentationMechanismFlag = process.env.HUB_ENV === 'testing' ? 2 : 0;
// Below mentioned keys are keys being published in the happy path
// Y = only publish to kafka, N = only publish via pubSub, M = publish to both
exports.pubSubKafkaKeys = {
  'deviceOrientation': 'N',
  'lastResponseTime': 'Y',
  'lastRequestTime': 'Y',
  'outsideBrowserstackTime': 'Y',
  'userHubLatency': 'Y',
  'customExecutorInstrumentation': 'Y',
  'seleniumRequestsCount': 'Y',
  'nonZeroStatusesCount': 'Y',
  'nonZeroIncrementCounters': 'Y',
  'exceptionEncountered': 'Y',
  'exceptionClass': 'Y',
  'exceptionRequest': 'Y',
  'exceptionMessage': 'Y',
  'insideHubTime': 'Y',
  'hubProcessingTime': 'Y',
  'nginxToHubTime': 'Y',
  'jarTime': 'Y',
  'sleepTime': 'Y',
  'sleepTimeNow': 'Y',
  'numSleep': 'Y',
  'request_count': 'Y',
  'timestamp': 'N',
  'lastRequest': 'N',
  'instable': 'N',
  'safariPrivoxyTimeout': 'Y',
  'toggle': 'M',
  "nudgeLocalNotSetError": "N",
  'userToNginxTime': 'Y',
  'elementNotFound': 'N',
  'pageLoadError': 'N',
  'selfHealingSuccess': 'N',
  'softHealingSuccess': 'N',
  'midSessionHealingDisabled': 'N',
  'automate_ai_duration': 'Y',
  'automate_ai_success': 'Y',
  'automate_ai_retry_count': 'Y',
  'automate_ai_find_element_count': 'Y',
  'automate_tcg_duration': 'Y'
};

exports.pubSubKafkaKeysProducerCodes = {
  'deviceOrientation': '_1',
  'lastResponseTime': '_2',
  'lastRequestTime': '_3',
  'outsideBrowserstackTime': '_4',
  'userHubLatency': '_p',
  'customExecutorInstrumentation': '_5',
  'seleniumRequestsCount': '_6',
  'nonZeroStatusesCount': '_7',
  'nonZeroIncrementCounters': '_8',
  'exceptionEncountered': '_9',
  'exceptionClass': '_a',
  'exceptionRequest': '_b',
  'exceptionMessage': '_c',
  'insideHubTime': '_d',
  'hubProcessingTime': '_e',
  'nginxToHubTime': '_f',
  'sleepTime': '_g',
  'sleepTimeNow': '_h',
  'numSleep': '_i',
  'request_count': '_j',
  'timestamp': '_k',
  'lastRequest': '_l',
  'instable': '_m',
  'safariPrivoxyTimeout': '_n',
  'updateOutsideBrowserstackTime': '_o',
  'userToNginxTime': '_z',
  'jarTime': '_p',
  'elementNotFound': '_v',
  'pageLoadError': '_u',
  'automate_ai_duration': '_w',
  'automate_ai_success': '_x',
  'automate_ai_retry_count': '_y',
  'automate_ai_find_element_count': '_B',
  'automate_tcg_duration': '_A'
};
exports.pubSubKafkaKeysConsumerCodes = Object.assign({}, ...Object.entries(this.pubSubKafkaKeysProducerCodes).map(([a,b]) => ({ [b]: a })));

// Below mentioned keys are only to be pushed to zombies from instrumentation service
exports.kafkaZombieKeys = [
  'bs_latency_mean',
  'inside_bs_network_time',
  'number_of_requests_nginx',
  'customer_session_duration',
  'last_opened_url',
  'first_opened_url',
  'selenium_requests_count',
  'tertiary_params',
  'outside_bs_time',
  'total_sleep',
  'num_sleep',
  'pre_quit',
  'percy_begin_time',
  'percy_number_of_tiles'
];

//Below keys are to be redacted before produced to kafka in instrumentation service
exports.instrumentationRedactionKeys = [
  'browserstack.aws.key',
  'browserstack.aws.secret',
  'browserstack.aws.save',
  'browserstack.logs.aws.secret',
  'browserstack.logs.aws.key',
  'browserstack.video.aws.key',
  'browserstack.video.aws.secret',
  'browserstack.stats.aws.key',
  'browserstack.stats.aws.secret',
  's3key',
  's3secret',
  'video_aws_keys',
  'video_aws_secret',
  'logs_aws_keys',
  'logs_aws_secret',
  'accesskey',
  'browserstack.overridelocalInfo.userAuthToken'
];

// Keys that need to be synced across host-processIds in the instrumentation service
exports.hilSyncKeys = ['seleniumRequestsCount','insideHubTime','hubProcessingTime','userToNginxTime','nginxToHubTime','jarTime','nonZeroStatusesCount','nonZeroIncrementCounters','request_count','customExecutorInstrumentation', 'automate_ai_duration', 'automate_ai_success', 'automate_ai_retry_count', 'automate_ai_find_element_count', 'automate_tcg_duration'];
// Keys that need to custom aggregation calculation
exports.hilCalcKeys = ['updateOutsideBrowserstackTime','lastRequestTime','lastResponseTime','sleepTime','numSleep','sleepTimeNow'];

exports.acceptSslffRequestTimeout = configFile["acceptSslffRequestTimeout"];
exports.acceptSslffReturnTimeout = configFile["acceptSslffReturnTimeout"];
exports.checkPageLoadWait = configFile["checkPageLoadWait"];
exports.callback_registry = {};
exports.commandScreenshot = fs.readFileSync(exports.ROOT_PATH + "config/commandlist.txt", "utf-8").split("\n");
exports.browserstackBuildVersionJSON = (new ConfigReader(exports.ROOT_PATH + ".browserstack_build_version.json", true)).getConfig();
exports.networkCommand = ["click","url","forward","back","refresh","submit"];
exports.browserConfig = (new ConfigReader(exports.ROOT_PATH + "config/browsers.json")).getConfig();
exports.browserstackParams = (new ConfigReader(exports.ROOT_PATH + "config/browserstackParams.json")).getConfig();
exports.NODE_DIED_IN = configFile["nodeTimeout"];
exports.longRequestTimeOffset = configFile["longRequestTimeOffset"];
exports.CDP_PLAYWRIGHT_PORT_DIFFERENCE = 200;
exports.START_REQUEST_TIMEOUT = configFile["startNodeTimeout"];
exports.SNAPSHOT_JAR_TIMEOUT = configFile["snapshotJarTimeout"] || 40000;
exports.IDLE_TIMEOUT = configFile["idleTimeout"];
exports.JS_EXECUTOR_TIMEOUT = 45000;
exports.BS_TIMEOUT = configFile["bsTimeout"];
exports.BS_RETRY_TIMEOUT = configFile["bsRetryTimeout"];
exports.SAME_RETRY_TIMEOUT = configFile["sameRetryTimeout"];
exports.maxAllowedDifferentMachineRetries = 1;
exports.newBucketing = (configFile["new_bucketing"] == undefined || configFile["new_bucketing"]) && true;
exports.DEFAULT_SEMAPHORE_TTL = configFile["default_semaphore_ttl"] || 600 * 1000; // 10 minutes
exports.WS_PROXY_HOST = configFile["wsProxyHost"];
exports.WS_PROXY_PORT = configFile["wsProxyPort"];
exports.WS_PROXY_SCHEME = configFile["wsProxyScheme"];
exports.terminal_session_count = configFile["terminal_session_count"] || 700;
// AppAutomate related data to push to EDS
exports.APP_AUTOMATE_TEST_SESSIONS_EDS_COLS = ["secondary_diagnostic_reason", "last_request", "start_session_time", "resolution", "customer_session_duration", "language", "implicit_timeout", "stop_time", "client_ip", "product"];
//hash in format {command: tag} for command usage tracking
exports.APP_AUTOMATE_APPIUM_COMMANDS_TO_COUNT = {
  "GET:source": "page_source",
  "POST:install_app": "install_app",
  "POST:reset": "reset_app",
  "POST:launch": "launch_app",
};

exports.APP_AUTOMATE_APPIUM_COMMANDS_TO_TRACK_DATA = {
  "POST:push_file": { tag: "push_file", fields: [ 'path' ] }
};

exports.BS_SCHEME = configFile["bs_scheme"] || "http";
exports.BS_ENDPOINT_PORT = exports.BS_SCHEME === "https" ? 443 : configFile["bs_port"];
exports.BS_ENDPOINT = configFile["bs_host"];
exports.BSTACK_API_HOST = configFile["bstack_api_host"];
exports.APP_AUTOMATE_BSTACK_API_HOST = configFile["app_automate_bstack_api_host"];
exports.BSTACK_API_PORT = configFile["bstack_api_port"];
exports.BSTACK_API_RETRIES = 2;

exports.BS_APP_ENDPOINT_PORT = exports.BS_SCHEME === "https" ? 443 : configFile["bs_app_port"];
exports.BS_APP_ENDPOINT = configFile["bs_app_host"];

exports.SERVER_PORT = configFile["hub_port"];
exports.SERVER_NAME = configFile["hub_host"];

exports.RPROXY_RETRIES = 3;
exports.RPROXY_RETRY_RESPONSE_TYPES = [400];

exports.LOG_POST_INTERVAL = configFile["log_post_interval"];
exports.env = configFile["env_name"];
exports.isTestingEnv = exports.env === 'testing';
exports.isProductionEnv = exports.env === 'Production';
exports.isLocalEnv = exports.env === 'local';
exports.isStagingEnv =  exports.env === 'Development';

const getEnvNameForTerminal = () => {
  if (exports.isProductionEnv) {
    return "production";
  } 
  if (exports.isStagingEnv) {
      return "staging";
  }
  return "development";
};
exports.getEnvNameForTerminal = getEnvNameForTerminal;
exports.envNameTerminal = getEnvNameForTerminal();

exports.lastLogTime = new Date();

exports.screenshotCountLogFolder = configFile["screenshot_count_log_folder"] || ".";
exports.workerExitPollInterval = configFile["worker_exit_poll_interval"] || 1000;
exports.workerExitHardTimeout = configFile["worker_exit_hard_timeout"] || 65000;

exports.blockedIPs = configFile["blocked_ips"];
exports.blockedUsers = configFile["blocked_users"];
exports.blockIPRegex = configFile["blocked_ips_regex"];
exports.blockedUserTimeout = configFile["blocked_user_timeout"];

exports.mailTo = configFile["mail_to"];

exports.haServer = configFile["ha_server"] + ":" + configFile["ha_port"];
exports.haTimeout = 100000;

exports.redisHost = configFile["redis_server"];
exports.redisPort =  configFile["redis_port"];
exports.redisAuth =  configFile["redis_auth"];

exports.redisNewConfig = configFile["redis_new_config"] || { port: configFile["redis_port"], host: configFile["redis_server"] };
// Second redis cluster. Will keep it pointed to the main cluster in case no second cluster.
exports.redisConfigSecond = configFile["redis_config_second"] || exports.redisNewConfig;
// Redis cluster for AI
exports.redisConfigAi = configFile["redis_config_ai"] || { port: configFile["redis_port"], host: configFile["redis_server"] };

exports.callbackHost = configFile["callback_server"] || configFile["redis_server"];

exports.logkey = configFile["logkey"];

exports.railstoken = configFile["railstoken"];

exports.hubName = configFile["hub_name"] || "unknown";

const hostname = os.hostname();
exports.osHostName = hostname;

exports.not_allowed_requests = configFile["not_allowed_requests"];

exports.region = configFile["browserstack.region"];
exports.metrics_region = configFile["metrics_region"];
exports.sub_region = configFile["browserstack.sub_region"] || (configFile["browserstack.region"] + "a");

exports.nonPipeUrls = configFile["non_pipe_urls"];

exports.zombie_server = configFile["zombie_server"];
exports.zombie_port = configFile["zombie_port"];
exports.ZOMBIE_MAX_STRING_SIZE = 2048;
exports.BLACKLISTED_ZOMBIE_KINDS = ['automation_session_stats', 'app_automation_session_stats'];

exports.eds_server = configFile["eds_server"];
exports.eds_port = configFile["eds_port"];
exports.eds_key = configFile["eds_key"];

exports.alert_host = configFile["alert_host"];
exports.alert_port = configFile["alert_port"];
exports.alert_path = configFile["alert_path"];

exports.other_hub_servers = configFile["other_hub_servers"];
exports.rails_omitted_caps = configFile["rails_omitted_caps"];
exports.rails_redacted_caps = configFile["rails_redacted_caps"];
exports.output_omitted_caps = configFile["output_omitted_caps"]; // Doesn't remove from raw logs
exports.omitDebugMode = configFile["omit_debug_mode"];
exports.safari_driver_port = configFile["safari_driver_port"];
exports.secondary_states = configFile["secondary_states"];
exports.chromeDriverNodes = configFile["chromeDriverNodes"];
exports.relaxed_start_timeout = configFile["relaxed_start_timeout"];
exports.relaxed_http = configFile["relaxed_http"];
exports.relaxed_http_timeout = configFile["relaxed_http_timeout"];
exports.rails_pipeline_queue_length_threshold = configFile.rails_pipeline_queue_length_threshold;
exports.rails_pipeline_queue_length_threshold_aa = configFile.rails_pipeline_queue_length_threshold_aa;

exports.QUEUE_REQUEST_DELAY = configFile["queue_request_delay"];
exports.MAX_SPACES_TIMES = (3 * 60 * 60 * 1000) / exports.QUEUE_REQUEST_DELAY; // max times we should send a space = 3 hours / delay between each space

// https://browserstack.atlassian.net/wiki/spaces/ENG/pages/1202094203/Datacenter+Regions+AWS+regions
exports.HUB_URL_SUB_REGIONS_MAPPING = configFile["hubUrlSubRegionMapping"] || {};

exports.SUB_REGION_HUB_URL_MAPPING = Object.assign({}, ...Object.entries(exports.HUB_URL_SUB_REGIONS_MAPPING).map(([k, v]) => Object.assign({}, ...v.map(x => ({ [x]: k })))));

exports.CDP_HUB_URL_SUB_REGIONS_MAPPING = configFile["cdpUrlSubRegionMapping"] || {};

exports.CDP_SUB_REGION_HUB_URL_MAPPING = Object.assign({}, ...Object.entries(exports.CDP_HUB_URL_SUB_REGIONS_MAPPING).map(([k, v]) => Object.assign({}, ...v.map(x => ({ [x]: k })))));

exports.MIN_VER_SDK_AGENT_MAPPING = {
  "nodeagent": "1.18.0",
  "pythonagent": "1.9.2",
  "javaagent": "1.7.1",
  "csharpagent": "1.1.1"
};

exports.QUEUED_REQUEST_EXPONENTIAL_DELAY = {
  DEFAULT_RETRY_DELAY: configFile["exponential_default_retry_delay"],
  QUEUE_MAX_WAIT: configFile["queue_max_wait"],
  HARD_RETRY_REQUEST_DELAY: configFile["hard_retry_request_delay"] || configFile["queue_request_delay"]
};

exports.NTA_RETRY_EXPONENTIAL_DELAY = {
  DEFAULT_NTA_RETRY_DELAY: configFile["exponential_nta_retry_delay"] || exports.QUEUED_REQUEST_EXPONENTIAL_DELAY.DEFAULT_RETRY_DELAY,
  NTA_RETRY_DEFAULT_DELAY: configFile["nta_retry_default_delay"] || exports.QUEUED_REQUEST_EXPONENTIAL_DELAY.HARD_RETRY_REQUEST_DELAY
};

exports.RESERVED_TERMINAL_QUEUEING = {
  RESERVED_TERMINAL_MAX_WAIT_TIME: 30000, // milliseconds
  RESERVED_TERMINAL_CUSTOM_RETRIES: 3 // no of retries
};

exports.HTTP_STR = "HTTP/1.1 ";
exports.HTTP_STR_LENGTH = this.HTTP_STR.length;
exports.emulator_boot_timeout = (this.HTTP_STR_LENGTH+1)*this.relaxed_start_timeout;
exports.ie_compatibility_modes = configFile["ie_compatibility_modes"];
exports.hub_status_data = configFile["hub_status_data"];
exports.terminal_cmd_exec_max_time = configFile["terminal_cmd_exec_max_time"];
exports.PAGE_LOAD_TIMEOUT = configFile["page_load_timeout"];
exports.DNS_TIMEOUT = configFile["dns_timeout"];

exports.ERROR = {
  HUB_TIMEOUT : 0,
  NETWORK_DOWN : 1,
  FFFF_DOWN: 2,
  CONN_FAILED_TWICE: 3,
  BROWSER_DIED : 4,
  OTHER: 5
};

exports.SESSIONS_STOP_STATE = {
  STOP_LIMIT : "stop_limit",
  STOP_SMD : "stop_smd",
  STOP_UI : "stop_ui",
  STOP_LIMIT_AA_FREEMIUM : "stop_limit_aa_freemium"
};

exports.INTERNAL_PAGELOAD_TIMEOUT_BROWSERS = [
	"internet explorer",
        "chrome",
        "firefox"
];

exports.CHUNKED_HEADER = {
  "Transfer-Encoding": "chunked",
  "Content-type": "application/json; charset=utf-8",
  "cache-control": "no-cache"
};

exports.SUCCESS_RESPONSE = {
  state: "success",
  status: 0,
  value: ""
};

exports.INVALID_COMMAND_RESPONSE = message => ({
  status: 9,
  value: {
    message,
  }
});

exports.LOG_LEVEL = {
  ERROR: 0,
  WARN: 1,
  REQUEST: 2,
  RESPONSE: 3,
  INFO: 4,
  DEBUG: 5,
  TRACE: 6
};

exports.REVERSE_MAP_LOG_LEVEL = {
  0: 'error',
  1: 'warn',
  2: 'request',
  3: 'response',
  4: 'info',
  5: 'debug',
  6: 'trace',
};

exports.hub_log_level = configFile["hub_log_level"];
exports.selenium_version_for_safari_driver = JSON.parse(fs.readFileSync(exports.ROOT_PATH + "config/selversion_for_safaridriver.json", "utf-8"));
exports.time_zones_mapping = JSON.parse(fs.readFileSync(exports.ROOT_PATH + "config/timezone.json", "utf-8"));
exports.default_safaridriver = configFile["default_safaridriver"];
exports.protractor_safaridriver = configFile["protractor_safaridriver"];
exports.default_seleniumjar_version = configFile["default_seleniumjar_version"];
exports.default_seleniumjar_port = configFile["default_seleniumjar_port"];

exports.sessionTimeout = configFile["sessionTimeout"] || "sessionTimeout";
exports.sessionDeletionChannel = configFile["sessionDeletionChannel"] || "sessionDeletionChannel";
exports.timeoutReinitialization = configFile["timeoutReinitialization"] || "timeoutReinitialization";
exports.updateKeyObject = configFile["updateKeyObject"] || "updateKeyObject";
exports.callbackStopDone = configFile["callbackStopDone"] || "callbackStopDone";
exports.userQueuePop = configFile['userQueuePop'] || 'userQueuePop';

exports.publishKeyCodes = {
  "lastRequestTime": "0",
  "outsideBrowserstackTime": "1",
  "sleepTime": "2",
  "numSleep": "3",
  "request_count": "4",
  "seleniumRequestsCount": "5",
  "insideHubTime": "6",
  "lastResponseTime": "7",
  "exceptionEncountered": "8",
  "lastRequest": "9",
  "hubProcessingTime": "h",
  "jarTime": "j",
  "nginxToHubTime": "n",
  "timestamp": "t",
  "userToNginxTime": "u",
  "userHubLatency": "l",
  "nudgeLocalNotSetError": "e",
  "selfHealingSuccess": "z",
  "softHealingSuccess": "p",
  "midSessionHealingDisabled": "q"
};

exports.subscribeKeyCodes = Object.assign({}, ...Object.entries(this.publishKeyCodes).map(([a,b]) => ({ [b]: a })));

// valid_values: 'new_approach', 'new_approach_sync', 'old_approach'
exports.parallelize_stages = {
  win: {
    browserstack_group: 'new_approach',
    all_users: 'new_approach'
  },
  mac: {
    browserstack_group: 'new_approach',
    all_users: 'new_approach'
  },
  android: {
    browserstack_group: 'new_approach',
    all_users: 'new_approach'
  }
};

exports.sessionQueuePrefix = configFile['sessionQueuePrefix'] || 'queue_';
exports.sessionParallelPrefix = configFile['sessionParallelPrefix'] || 'parallel_';
exports.sessionFTPrefix = configFile['sessionFTPrefix'] || 'ft_';
exports.sessionQueueResetTime = configFile['sessionQueueResetTime'] || (2 * 60);

exports.emulatorDeviceSizes = configFile["emulatorDeviceSizes"];
exports.realDeviceSizes = configFile["realDeviceSizes"];

exports.cls_port = configFile["cls_port"];
exports.cls_host = configFile["cls_host"];

exports.S3_UPLOADER_QUEUE = configFile["s3_uploader_queue"];
exports.APP_AUTOMATE_S3_UPLOADER_QUEUE = configFile["app_automate_s3_uploader_queue"];

exports.AUTOMATE_SPAMMY_URLS = configFile["spammy_redis_key"];
exports.AUTOMATE_LOGS_V2_S3_EXPIRY_TIME = configFile["s3_redis_key_expiry_time"];

// Privoxy related config
exports.PRIVOXY_USER_KEEP_ALIVE_TIMEOUTS = "PRIVOXY_USER_KEEP_ALIVE_TIMEOUTS";
exports.PRIVOXY_KEEP_ALIVE_TIMEOUTS = configFile["privoxy_keep_alive_timeouts_key"];

exports.AUTOMATE_SESSION_WITH_STOP_FAILED = configFile["max_stop_retry_failed"];
exports.APP_AUTOMATE_SESSION_WITH_STOP_FAILED = configFile["app_automate_max_stop_retry_failed"];

exports.APP_AUTOMATE_CURRENT_SESSIONS = configFile["app_automate_current_sessions_redis_key"];

exports.allowedFileSizeBytes = 30 * 1024 * 1024;
exports.uploaderRange = 256*1024;
exports.partialLogsLimit = 1024*1024;
exports.uploaderMetadata = {};
exports.AWS_NEW_USER_ENABLED_GROUPS = [];
exports.AWS_NEW_USER_ENABLED_ALL = false;
exports.LOGS_AWS_KEY = configFile["logs_aws_key"];
exports.LOGS_AWS_SECRET = configFile["logs_aws_secret"];
exports.LOGS_AWS_BUCKET = configFile["logs_aws_bucket"];
exports.LOGS_PUT_AWS_KEY = configFile["logs_put_aws_key"];
exports.LOGS_PUT_AWS_SECRET = configFile["logs_put_aws_secret"];
exports.AWS_STANDARD_STORAGE_CLASS = "STANDARD";
exports.AWS_MIN_OBJECT_SIZE = 131072; // 128 KB

exports.DEBUGSCREENSHOT_AWS_KEY = configFile["debugscreenshot_aws_key"] || exports.LOGS_AWS_KEY;
exports.DEBUGSCREENSHOT_AWS_SECRET = configFile["debugscreenshot_aws_secret"] || exports.LOGS_AWS_SECRET;
exports.DEBUGSCREENSHOT_PUT_AWS_KEY = configFile["debugscreenshot_put_aws_key"] || exports.DEBUGSCREENSHOT_AWS_KEY;
exports.DEBUGSCREENSHOT_PUT_AWS_SECRET = configFile["debugscreenshot_put_aws_secret"] || exports.DEBUGSCREENSHOT_AWS_SECRET;
exports.DEBUGSCREENSHOT_DEPRECATED_OS = ["macsl", "maclion"];

exports.APP_AUTOMATE_LOGS_AWS_KEY = configFile["app_automate_logs_aws_key"];
exports.APP_AUTOMATE_LOGS_AWS_SECRET = configFile["app_automate_logs_aws_secret"];
exports.APP_AUTOMATE_LOGS_AWS_BUCKET = configFile["app_automate_logs_aws_bucket"];
exports.autoitSendKeysSleep = configFile["autoit_sendkeys_sleep"];
exports.autoitSendKeysMaxSleep = configFile["autoit_sendkeys_max_sleep"];
exports.autoitSendKeysMinSleep = configFile["autoit_sendkeys_min_sleep"];

// For AirBnb (and other customers if required) as they are running huge sessions in loop with bare minimum commands leading to continuous NTAs.
exports.SESSION_DELETE_RESPONSE_DELAY = 60 * 1000;
exports.DELAY_ON_DELETE_RESPONSE_FOR_GROUP = 'DELAY_ON_DELETE_RESPONSE_FOR_GROUP'; // redis key to keep the group id, i.e. SADD DELAY_ON_DELETE_RESPONSE_FOR_GROUP <group_id>
exports.ENABLE_JAR_DELETE_FOR_GROUP = 'ENABLE_JAR_DELETE_FOR_GROUP'; // redis key to keep the group id, i.e. SADD ENABLE_JAR_DELETE_FOR_GROUP <group_id>
exports.DEVICES_WITH_DELAYED_DELETE_RESPONSE = [/iphone.+11\W*$/i];
exports.SESSION_TIME_THRESHOLD_FOR_DELAY_IN_DELETE_RESPONSE = 10 * 1000; // Doesn't include the browser initialization time
exports.BS_STOP_TIMEOUT = 1000 * 60 * 10; //timeout in 10 mins
exports.FIREFOX67_PROFILE = "UEsDBBQAAAAIAHmot05JuhROlwMAAAkNAAAHAAAAdXNlci5qc6VWy27cOBC871cEPm2ADDF2NpfNyXG8wAJBvMjAyJGgyNaIHopkyObI+vs0pZEzsfVK9qZHFZus7q5mihC4D1D+eVEE19AbU65mjbbKNUyl2jOwojCgLt68wpDg9fs/0kuOEQghJHsGLoWJE2gLDYqCpWAIdyEKl/Dvwgh7uJjDe7GHdetHFAGTZ5lByO08qnI1nJAr9vKcxd2RTq4VsDqis90qem9dgIkF6JO6s187gePllmmLwd1/+TQfPielFo9cVoEC8yiD9shJcY66zkHfbscJ65ABvAvIhTH8IXJ4lOBROxtHk/4gjqJflrkexmLlmn/tDT06A6MkC9i4cGBSEOUImxxPmA0Bjlou5DWCTEFjy2QcynEC+Dw5DRiZ9VostTkmE0rpfE6xsEgDhQp0uMCFzAryRLlNEUFxCQHH1TwjxZhyxCeSpg8QZuX0wT22jPLmGl7pByEP2u65cVKYykVciNhrya3IKeFwBDuxSeE9S15RlzOR0I2rfwZa1aZKR29EC+r2crv77JDqgAh/TYBdY40TioraUn5CV3JfK7C7nDk69Hys2/tP12xo1Vkr65BvWZ/BJdvT9kA9AJZahgywL5CrJSzZWe+v02BXloSfqPIBFECorAwgSNrpPzpEvCYt5EQLPlmYKKF7JtVWGuo5oxamEWGtFYMIsjqVxRLUlDH5bAzDaJjyYIgx2w6Sy5IKXdeU5ItcBhGrhSgVGMNkBfLwEUqRDH7o/8zTaP5ERse2d/bGuLhwlDP0HWV8HEyKiN53s6oVCINV/85WueIMP/ncKre/yR6il7mgvqRf375x+33+KPt58Av78M5o2bL8cZeKWnd5/r/8/7qP1z8aemwZOKJzJjJyCBdOG5+9+uSxSQ7WGWjX1opmVOe9YiJG9gjbFW5noh978k4SO7vu5XYSXpChH4yOOF8RZ4yuwG9c7cnaC23y8LR6X6FpF6lrLPwl3JJ/l+39ZCOdXRrmVB3mWu/ym5MPbmg8Y4rjKw+UCpGufJWOVbvJvz3N040Bu8dsCVfv3v1MG5amqRVPA7Roueo9YXRvNGtrjzF3N6+dEmblraXUBuiJuaCpLXhfo/kitoCPGLRE/pw2FirfOC1ZYQ11AWHXvU7Ilcv8oJGdZBvG6amURw8+UBAMRcDQsl6K7uRXS9hZkV7CAzx042x0J49ek+1Ttlg+MtUEuT8xviUdpiL8oDSVpjC5i0YY3wFQSwECFAMUAAAACAB5qLdOSboUTpcDAAAJDQAABwAAAAAAAAAAAAAApIEAAAAAdXNlci5qc1BLBQYAAAAAAQABADUAAAC8AwAAAAA=";
exports.FIREFOX65_PROFILE = "UEsDBBQAAAAIAMGVRU4tDkNWygMAABkOAAAHAAAAdXNlci5qc6VWS2/cNhC+91cUPqVAl1g7zaU9FI7jAgWCusjCyJGgyNGKXopkyeHK++87lFaxvNYr7U2P7+MM5/HNpAiB+wDlu6siuIbemHI1a7RVrmEq1Z6BFYUBdfXzjxgS/PTbD+ktxwiEEJIdgEth4gTaQoOiYCkYwl2JwiX8tTDCHq7m8F7sYd35EUXA5FlmEHI7j6pcDWfkCl8uWdwd6eZaAasjOtueovfWBZg4gD6pB/u1DXC83jJtMbjHL58vzP9+Qc9ZqcUzl1UgyzzKoD1yCjlHXWer77fjhHXIAN4F5MIY/hQ5PEvwqJ2No1l/EkfRHctcB2Oxcs2f9o4enYFRkgVsXDgwKYhyhE22J8yGAEctFxIbQaag8cRk7OtxAniZnQaMzPFarLU5JhNK6XxPsXBIA4UKdLnAhcwR5ImSmyKC4hICjkdzQIoxZYvfSJo+QJjtPClkBUzpeHgJzBK6htqF03q8K0ujLcwR+uxWiD6X+KZlzkJ9cM8nFp08RB7IIwSuJgruNcU6nh80RO5sTshFFoQnF7wiSWIioRuvlAFolaZQhL0RJ1D319vdXw6pZonwywTYNdY4oagBLdVSaNvjawV2l6tM2/28rfvHz7es15XZ/LTI96yrtiWN1vZA/QqW2pvUuivmmyUsaW83DKbB5/KYv1MAoXJkAEGSp3/oEPGWYiEXKjCKEtpnitpK9R8yamEaEdbODRBBVueyWIKaMiafRayfY1MDA2LMEok0EigKbYeXpOFcBhGrBSsVGMOokeThE5QiGfzY/Zmn0bCMjK5tH+ydcXHhKgP0A2V8HEwREd2MyFGtQBisune2SsFn+MnnVrn/j+zeepkL6kv6fveN2+/zR9nNru/wwzuj5Ynlj7tU1LrN8//l/91+vH1p6LFj4IjOmchIIVw4Oz67p+URTwqWAbxta0Xz1O55jvy4jawRti3cVkQ/deSdJHbW6OvtJLwwJOlGR5yviAGjLfA7V3uButAmD3qr9xWa0yK1z97c5QfwNYr/Fm5J7svT42TfDfahOT/6KdYNhc1ZNje0eWCK4ye/Gqu+0rE6bfJvT6vCxoDdY1aQmw8fXtP6o2nIUfqMcQ0vTlx1EjLqGw3U2mPMYsBrp4RZuZCV2gA9MRc05YF3JZ13zAV8xKAl8kvamKm8TVtSTtpaCgi79nUiXLkrDhrZOWz99D1X/ujFewqCIQtIa1EXivbmN0vY2SC9hQd4aqffqCfPXtOUoGyxfGWqCRoWxPgn6TBl4YXSVJrM5KabZ7wsm53rnAzRK4cjWBxG9V9QSwECNAMUAAAACADBlUVOLQ5DVsoDAAAZDgAABwAAAAAAAAABAAAApIEAAAAAdXNlci5qc1BLBQYAAAAAAQABADUAAADvAwAAAAA=";
exports.FIREFOX_PROFILE = "UEsDBBQAAAAIADNwWUmK7I+M9AMAAJUPAAAHAAAAdXNlci5qc6VXTW/jNhC991cUOW2Bmkiy3Ut7KLLZFCiwaIo1gj0SFDmyGFMkSw4t+993KNmJ40iU3d5E+j3OcD7e0ClC4D5A/eGqCq6jFVOuZZ22ynVMpdYzsKIyoK5+/hFDgp9++yG95xiBEEKyR+BamDiBttChqFgKhnBXonIJf62MsOurEt6LFZx3fkQRMHmWGYS8LqMa18IeeYYvtKUe7fc+PvHmmmmLwT19+3rC/v2EnoPaii2XTSBzPMqgPXKKGEfdZtMfr8cJ5yEDeBeQC2P4c+SwleBROxtHk/YsNmI4lrkBxmLjuj/tPX06A6MkC9i5sGYNos+OLaSzFmRPX3gIC4JuIBD35sS/0zjzDozMQZjNf4nJhFI6Wxczh3RQqaDJNS5kDgtPlLEUERSXEHA8REekGFO2+ELStNHfc7obpJANMKXjel+v5XId4C20LuwuILi6NtpCkfEma7S/6KllrA9uu2PRyXXkgZxC4GqikoSnU72i5mcioRs/9wh0VvdS3LwRO1APN9fLvxxqmR3+ZQLsOmucUFSSliok9JX8vQG7zLWj7aps6+Hp6x1zlOeg1Xjdv0F+ZEMNzamhtmtqLbDUiaSLQ4nezmFJ5QbZnQbvU16+UwChcmQAqT1B/aFDxDuKhZzo7Bc9FDX03xS1M3X2mNEK04lwrkKDCLLZl8Uc1NQx+axvh4kxIc0RYsxyhI7cCND3bU1yy2UQsZmx0oAxjHpDrr9ALZLBz8MvZRqNpcjo2vbR3hsXZ65yhH6kjI+DKSJikPMc1QaEwWZYsyyx1A3lEBf4yedWefiP7IP1OhfUt3S5+8atVnlTDmPmAj+8M1ruWN5cpqrVfZ7/L//vfvPutaHHjoENOmciI4VwYe948UWUpzEpWAbwvq1VCuQCz5Eft5E1wvaF24vol4G8lMSOI+P0CF4ZEmmjI5Yr4ojRF/i9a71AXWmjccesXjVodrPUQ/ZKlz+Cn6P47+GW5L7ePU323dHTpeTHYZQNQ2Gxl80FvScwxTMmpW90bHaL/LOnB8DCgF1hVpDbT5/e0g5H05Cj9BnjOl7tuBokZNQ3Gq+tx5jFgLdOCVOOUQRJBUR5ktEXR/0LsNYG6Iu5oClhfKj9/G6cwUcMWiI/pZVMZS3jYGnG5RLvt+e8G6P0Q5s7Ky8jdyDWFxMus2VAbC672lvGZdZi1ibk2p5vbqOhy+ZavZ2toPeEWff0yjpLs5QepxWEZb+caKCsk2uNbN9Ih/fYXgtHW+FAQTBkAen1OzRHf5PbOWyxbd7DAzz376FRT7aego7UvyxfmVSCng/E+CfpMGXhldI1msxkGS4zXv9UDK5zMkRLDhuq0OOo/gtQSwECNAMUAAAACAAzcFlJiuyPjPQDAACVDwAABwAAAAAAAAABAAAApIEAAAAAdXNlci5qc1BLBQYAAAAAAQABADUAAAAZBAAAAAA";
exports.FIREFOX_PROFILE_WITH_NATIVE_EVENTS = "UEsDBBQAAAAIALOhdlhdDdFu9AMAAJQPAAAHABwAdXNlci5qc1VUCQADGZn9ZRmZ/WV1eAsAAQT3AQAABBQAAAClV01v4zYQvfdXFDltgZpIst1Leyiy2RQosGiKNYI9EhQ5shhTJEsOLfvfdyjZieNIlN3eLOY9znA+****************************************+vlHDAl++u2H9J5jBEIIyR6Ba2HiBNpCh6JiKRjCXYnKJfy1MsKur0p4L1Zw3v0RRcDkWWYQ8rqMalwLe+QZvtCRerTf+/jEm2umLQb39O3rCfv3E3oOaiu2XDaBzPEog/bIKWIcdZtNf7weJ5yHDOBdQC6M4c+Rw1aCR+1sHE3as9iI4VrmBhiLjev+tPf00xkYJVnAzoU1axB9dmwhnbUge/rCQ1gQdAOBuDcn/p3GmXdgZA7CbP5LTCaU0tm6mLmkg0oFTa5xIXNYeKKMpYiguISA4yE6IsWYssUXkqaD/p3T3SCFbIApHdf7ei2X6wBvoXVhdwHB1bXRFoqMN1mj80VPLWN9cNsdi06uIw/kFAJXE5UkPN3qFTU/Ewnd+L1HoLO6l+LmjdiBeri5Xv7lUMvs8C8TYNdZ44SikrRUIaGv5O8N2GWuHW1XZVsPT1/vmKM8B63G6/4N8iMbamhODbVdU2uBpU4kXRxK9HYOSyo3yO40eJ/y8psCCJUjA0jtCeoPHSLeUSzkRGe/6KGoof9NUTtTZ48ZrTCdCOcqNIggm31ZzEFNHZPP+naYGBPSHCHGLEfoyI0Afd/WJLdcBhGbGSsNGMOoN+T6C9QiGfw8/KVMo7EUGT3bPtp74+LMU47Qj5TxcTBFRAxynqPagDDYDN8sSyx1QznEBX7yuVUe/iP7YL3OBfUtXe6+catVPpTDmLnAD++MljuWD5epanWf5//L/7s/vHtt6LFrYIPOmchIIVzYO17ciPI0JgXLAN63tUqBXOA58uM2skbYvnB7Ef0ykJeS2HFknB7BK0MibXTEckUcMfoCv3etF6grbTTumNWrBs1ulnrIXunxR/BzFP893JLc17unyb47Wl1KfhxG2TAUFnvZXNA+gSmeMSl9o2OzW+Q/e1oAFgbsCrOC3H769JZ2uJqGHKXPGNfxasfVICGjvtF4bT3GLAa8dUqYcowiSCogypOMvjjqX4C1NkC/mAuaEsaH2s974ww+YtAS+SmtZCprGQdLMy6XeH88590YpR/a3Fl5GbkDsb6YcJktA2Jz2dPeMi6zFrM2Idf2fHMbDV021+rtbAW9J8y6p1fWWZqltJxWEJb950QDZZ1ca2T7RjrsY3stHG2FAwXBkAWk7Xdojv4lt3PYYtu8hwd47vehUU+2noKO1L8sP5lUgtYHYvyTdJiy8ErpGk1msgyXGa//VAyuczJEnxw2VKFHC/a/UEsBAh4DFAAAAAgAs6F2WF0N0W70AwAAlA8AAAcAGAAAAAAAAQAAAKSBAAAAAHVzZXIuanNVVAUAAxmZ/WV1eAsAAQT3AQAABBQAAABQSwUGAAAAAAEAAQBNAAAANQQAAAAA";
exports.FIREFOX_BETA_PROFILE = "UEsDBBQAAAAIABiheU5JuhROlwMAAAkNAAAHAAAAdXNlci5qc6VWy27cOBC871cEPm2ADDF2NpfNyXG8wAJBvMjAyJGgyNaIHopkyObI+vs0pZEzsfVK9qZHFZus7q5mihC4D1D+eVEE19AbU65mjbbKNUyl2jOwojCgLt68wpDg9fs/0kuOEQghJHsGLoWJE2gLDYqCpWAIdyEKl/Dvwgh7uJjDe7GHdetHFAGTZ5lByO08qnI1nJAr9vKcxd2RTq4VsDqis90qem9dgIkF6JO6s187gePllmmLwd1/+TQfPielFo9cVoEC8yiD9shJcY66zkHfbscJ65ABvAvIhTH8IXJ4lOBROxtHk/4gjqJflrkexmLlmn/tDT06A6MkC9i4cGBSEOUImxxPmA0Bjlou5DWCTEFjy2QcynEC+Dw5DRiZ9VostTkmE0rpfE6xsEgDhQp0uMCFzAryRLlNEUFxCQHH1TwjxZhyxCeSpg8QZuX0wT22jPLmGl7pByEP2u65cVKYykVciNhrya3IKeFwBDuxSeE9S15RlzOR0I2rfwZa1aZKR29EC+r2crv77JDqgAh/TYBdY40TioraUn5CV3JfK7C7nDk69Hys2/tP12xo1Vkr65BvWZ/BJdvT9kA9AJZahgywL5CrJSzZWe+v02BXloSfqPIBFECorAwgSNrpPzpEvCYt5EQLPlmYKKF7JtVWGuo5oxamEWGtFYMIsjqVxRLUlDH5bAzDaJjyYIgx2w6Sy5IKXdeU5ItcBhGrhSgVGMNkBfLwEUqRDH7o/8zTaP5ERse2d/bGuLhwlDP0HWV8HEyKiN53s6oVCINV/85WueIMP/ncKre/yR6il7mgvqRf375x+33+KPt58Av78M5o2bL8cZeKWnd5/r/8/7qP1z8aemwZOKJzJjJyCBdOG5+9+uSxSQ7WGWjX1opmVOe9YiJG9gjbFW5noh978k4SO7vu5XYSXpChH4yOOF8RZ4yuwG9c7cnaC23y8LR6X6FpF6lrLPwl3JJ/l+39ZCOdXRrmVB3mWu/ym5MPbmg8Y4rjKw+UCpGufJWOVbvJvz3N040Bu8dsCVfv3v1MG5amqRVPA7Roueo9YXRvNGtrjzF3N6+dEmblraXUBuiJuaCpLXhfo/kitoCPGLRE/pw2FirfOC1ZYQ11AWHXvU7Ilcv8oJGdZBvG6amURw8+UBAMRcDQsl6K7uRXS9hZkV7CAzx042x0J49ek+1Ttlg+MtUEuT8xviUdpiL8oDSVpjC5i0YY3wFQSwECFAMUAAAACAAYoXlOSboUTpcDAAAJDQAABwAAAAAAAAAAAAAApIEAAAAAdXNlci5qc1BLBQYAAAAAAQABADUAAAC8AwAAAAA=";

exports.SAFARI_CANCEL_TIMEOUT = configFile["safari_cancel_timeout"];
exports.EDGE_AUTH_TIMEOUT = configFile["edge_auth_timeout"];
exports.DEFAULT_ANDROID_VIDEO_HEIGHT = configFile["defaultAndroidVideoHeight"];
exports.DEFAULT_ANDROID_VIDEO_WIDTH = configFile["defaultAndroidVideoWidth"];
exports.ANDROID_VIDEO_RESOLUTION = configFile["androidVideoResolution"];
exports.IE11W10_FILE_UPLOAD_TIMEOUT = configFile["ie11w10_file_upload_timeout"];
exports.BASIC_AUTH_IOS_TIMEOUT = configFile["default_basic_auth_ios_timeout"];
exports.BASIC_AUTH_IOS_RESPONSE_TIMEOUT = configFile["default_basic_auth_ios_response_timeout"];
exports.PAGE_LOAD_IOS11_TIMEOUT = configFile["default_page_load_ios11_timeout"];
exports.PAGE_LOAD_DEFAULT_TIMEOUT = configFile["default_page_load_timeout"];
exports.DISABLE_INSECURE_MAX_ATTEMPTS = 5;
exports.TITLE_CHECK_RETRY_TIMEOUT = 500; //500ms

exports.USE_LONGJOHN = configFile["use_longjohn"] || false;
exports.LOG_TYPES = ["client", "server", "driver", "browser", "performance"];
exports.maxNonZeroDayCount = configFile["max_non_zero_day_count"];
exports.whitelisted_chromeoptions_profile_keys = ['default_content_settings', 'content_settings', 'default_content_setting_values', 'managed_default_content_settings', 'gaia_info_picture_url', 'block_third_party_cookies'];

exports.directChromeDriverVersions = [42, 55, 56, 57];
exports.DEFAULT_APP_DOWNLOAD_TIMEOUT = 30;
exports.APP_DOWNLOAD_TIMEOUT_CAP = "browserstack.appDownloadTimeout";

// Extra options for testing
exports.disableLogs = configFile["disableLogs"] || false;
exports.forceCheckTitleAfterOpenUrl = configFile["forceCheckTitleAfterOpenUrl"] || false;
exports.push_node_hooothoot_interval = configFile['pushNodeHoothootInterval'] || 60 * 1000; // 1 minute interval
exports.clearSemaphoreInterval = configFile['clearSemaphoreInterval'] || 60 * 1000; // 1 minute interval
exports.uploadWebDriverLogs = configFile['uploadWebDriverLogs'] || true;

exports.consoleLogsQueue =  "queue_console_logs";
exports.blocked_requests = ["/appium/app/reset", "/appium/device/is_locked", "/appium/device/open_notifications", "/appium/device/shake", "/appium/device/open_notifications", "/appium/device/toggle_airplane_mode", "/appium/start_recording_screen", "/appium/stop_recording_screen"];
exports.blocked_requests_app_automate = ["/appium/device/shake", "/appium/device/toggle_airplane_mode", "/appium/start_recording_screen", "/appium/stop_recording_screen"];

const appiumPushFileBasePaths = ["/mnt/sdcard", "/sdcard", "/storage/emulated/0", "/storage/self/primary"]; // All are symlinks to /sdcard/
const appiumPushFileDirs = ["/Pictures/", "/Download/", "/Android/data/"];
exports.whitelistedAppiumPushFilePaths = [].concat(...appiumPushFileBasePaths.map(basePath => appiumPushFileDirs.map(dir => basePath + dir))); // Cartesian Concat of appium push_file base path and file_dirs

exports.hoothootHubNodeStatsKey = 'automate_hub_stats';

exports.activeWindowCheckBrowsers = [ "chrome", "safari", "firefox" ];
exports.activeWindowLongPageLoadTime = 2 * 60 * 1000; // 2 minutes
exports.activeWindowBasicAuthValues = [ 'authorization required', 'authentication required', 'do you want to leave this site?', 'microsoft internet explorer', 'password required' ];

exports.httpHostConnectExceptionRetryTimeout = 5 * 1000; // 5 secs
// Max Log file size that the uploader will upload, in bytes.
exports.max_log_file_size = 1073741824;
exports.DEFAULT_CONSOLE_LOGS_DATA = `//------------------------------------------------------------------------------
//     No messages were logged in this Session.
//     This note was auto-generated by BrowserStack.
//
//     Date: <date>
//     Session Id: <session_id>
//------------------------------------------------------------------------------`;
exports.hoothootKafkaUploaderStatsKey = 'hub_kafka_uploader_stats';
exports.defaultFileType = 'unknown_filetype';
exports.TITLE_CHECK_MAX_ATTEMPTS = 5;
exports.SAFARI_INSECURE_PAGE_TITLE = 'This Connection Is Not Private';
exports.SAFARI_BASIC_AUTH_BELOW_IOS10_TITLE = 'Possible Phishing Website';

exports.hoothootMonitoringServiceStatsKey = 'hub_monitoring_service_stats';

exports.customErrorTypes = {
  userRequestNotJSON: "BStackError: JSONParseError"
};

exports.ADDITIONAL_JSON_CAP_PW_ON_IOS = 'ADDITIONAL_JSON_CAP_PW_ON_IOS';

// Size limits are in Bytes
exports.LIMIT_RAW_LOGS_SIZE = [{
  "minRequests": 0,
  "maxRequests": 10,
  "sizeLimit": 5 * 1024 * 1024 // 5 MB
}, {
  "minRequests": 11,
  "maxRequests": 100,
  "sizeLimit": 2 * 1024 * 1024 // 2 MB
}, {
  "minRequests": 101,
  "maxRequests": 500,
  "sizeLimit": 1 * 1024 * 1024 // 1 MB
}, {
  "minRequests": 501,
  "maxRequests": Number.MAX_SAFE_INTEGER,
  "sizeLimit": 100 * 1024 // 100 KB
}];

// Used for fetching console logs for Firefox.
exports.foxDriverServerPort = 45699;

exports.exceptionAlertReceivers = configFile.exceptionAlertReceivers || "devnull";
exports.exceptionAlertDelay = 2000; // 2 secs



// Redis key has the value of the process PID of the node
// process which will monitor Redis, push to hoothoot and send Alerts.
exports.owner_redis_alerts_redis_key = "owner_of_redis_alerts";


// Constants related to railsDefender
exports.railsDefenderConfig = {

  // Redis key prefix which identifies users who needs to be served from Hub
  cache_keys: {
    // because of incorrect credentials.
    bad_auth: "temp_blocked_user_auth",
    // because they have an expired automate plan.
    expired_plan: "temp_blocked_user_expired_plan",
    // because their testing time expired
    expired_testing_time: "temp_blocked_user_expired_testing_time",
    // because local binary is disconnected
    binary_disconnected: "temp_blocked_user_binary_disconnected",
    // because local binary is disconnected
    generic: "temp_blocked_user_generic"
  },

  bq_error_codes: {
    // adding _cached_response will help on bq to differentiate
    binary_disconnected : "local_connection_error_cached_response"
  },

  // Expiry of each blocked_* keys, in seconds.
  cacheExpiry: 40,

  aa_cacheExpiry: 60
};


configFile.railsPipeline = configFile.railsPipeline || {};

exports.railsPipeline = {
  pollQueuedDelay: configFile.railsPipeline.pollQueuedDelay || 5 * 100, // 500 msecs - Delay between next pipeline check if a request is queued
  pollEmptyDelay: configFile.railsPipeline.pollEmptyDelay || 50, // 50 msecs - Delay between next pipeline check if pipeline is empty
  redisAutomateTrackTokenHash: {
    response200Token: 'pipeline::automate::trackToken:200',
    responseNon200Token: 'pipeline::automate::trackToken:Non200',
    ntaSoftMobileTokenHash: 'pipeline::automate::trackNTA::Soft::RealMobile',
    ntaSoftDesktopTokenHash: 'pipeline::automate::trackNTA::Soft::Desktop',
    ntaHardMobileTokenHash: 'pipeline::automate::trackNTA::Hard::RealMobile',
    ntaHardDesktopTokenHash: 'pipeline::automate::trackNTA::Hard::Desktop',
    queueCountMaxTag: 'pipeline::maxRailsRequests', // tag for redis key to denote max number of rails requests in the system
    downtimeToken: 'pipeline::downtime::automate', // Value should be one of constants.monitoring.downtimeStates.redisValue
    queueTrackRequestsSetTag: 'pipeline::automate::trackSet', // set to track current requests for automate
    userErrorToken: 'pipeline::automate::userName'
  },
  redisAppAutomateTrackTokenHash: {
    response200Token: 'pipeline::appAutomate::trackToken:200',
    responseNon200Token: 'pipeline::appAutomate::trackToken:Non200',
    ntaSoftMobileTokenHash: 'pipeline::appAutomate::trackNTA::Soft::RealMobile',
    ntaSoftDesktopTokenHash: 'pipeline::appAutomate::trackNTA::Soft::Desktop',
    ntaHardMobileTokenHash: 'pipeline::appAutomate::trackNTA::Hard::RealMobile',
    ntaHardDesktopTokenHash: 'pipeline::appAutomate::trackNTA::Hard::Desktop',
    queueCountMaxTag: 'pipeline::maxAppAutomateRailsRequests',
    downtimeToken: 'pipeline::downtime::appAutomate', // Value should be one of constants.monitoring.downtimeStates.redisValue
    queueTrackRequestsSetTag: 'pipeline::appAutomate::trackSet' // set to track current requests for app automate
  }
};

exports.pipelineManuplationValues =  configFile["pipelineManuplationValues"];

// Rails error responses
exports.railsResponses = {
  bad_auth: "Invalid username or password",
  expired_plan: "Your Automate plan has expired",
  expired_testing_time: "Automate testing time expired.",
  binary_disconnected: "[browserstack.local] is set to true but local testing through BrowserStack is not connected."
};

// Constants related to timeoutManager
exports.timeoutManager = {
  interval: 1000, //time between consecutive zrange operations
  zsetKey: 'timeoutManager_v3',
  logsPath: '/ebs/',
  // Block redis connection to wait for event (in secs)
  // Increasing leads to longer times for process to die / restart (0 is infinite)
  // Decreasing leads to more frequent queries for redis
  // waitForEvent is not needed since we are not using blpop currently
  // waitForEvent: 5,
  lpopFrequencyIntervalDuration: configFile['timeoutManager']['lpopFrequencyIntervalDuration'] || 200, //200ms seconds recall interval
  totalCountThisRegion: configFile["timeoutManager"]["totalCountThisRegion"],
  eventsListPrefix: "timeoutManager",
  backupListPrefix: "backup",
  redisKeyPrefix: "masterName",
  timeoutFailedRetryDelay: 10 * 1000, // 10 seconds
  restoreTimeoutsBatchSize: 10,
  globalExceptionHootHootKey: 'timeout_manager_global_exception',
  statsKey: 'timeout_manager_stats',
  pushToHootHootInterval: 60 * 1000, // 1 minute
  // delay while checking for master if service is not already master
  pingForMasterInterval: 10 * 1000, // 10 seconds
  // service will declare itself as master if ack from any other service is older than this
  ackLimitForMaster: 60 * 1000, // 1 minute
  // service will verify if it is currently master ( after it declared itself as master )
  verifyMaster: 60 * 1000, // 1 minute
  // delay for sending acks if service is master
  ackAlive: 10 * 1000, // 10 seconds
  backlogThresholdAlertLimit: 10000,
  becomeMasterScriptPath: exports.ROOT_PATH + 'script/lua_scripts/becomeMaster.lua',
  unsetMasterScriptPath: exports.ROOT_PATH + 'script/lua_scripts/unsetMaster.lua',
  hoothootPerMinKeys: [ 'lastMinIdleTimeoutRetries', 'lastMinIdleTimeoutRetryFailures', 'lastMinIdleTimeouts', 'lastMinClearTimeouts', 'lastMinClearTimeoutErrors', 'lastMinUpdateTimeouts', 'lastMinMaxDelay', 'lastMinMaxLoopTime', 'lastMinMaxRedisRTT' ],
  rpushTag: 'timeoutManagerRPushRate',
  zaddTag: 'timeoutManagerZAddRate',
  zremTag: 'timeoutManagerZRemRate',
  hubServer: configFile['timeoutManager']['hubServer'] || '127.0.0.1',
  hubPort: configFile['timeoutManager']['hubPort'] || 8080,
};

exports.queueingRelatedEdsConst = {
  keyPrefix: 'userIdFor_',
  timeout: 15 * 60 * 1000,
  reason: 'queue_size_exceeded',
};

exports.redisBecomeMasterScripts = {
  becomeMaster: {
    numberOfKeys: 1,
    scriptPath: 'becomeMaster.lua',
  },
  unsetMaster: {
    numberOfKeys: 1,
    scriptPath: 'unsetMaster.lua',
  },
};

exports.manipulateNumberScripts = {
  manipulateNumber: {
    numberOfKeys: 1,
    scriptPath: 'manipulateNumber.lua',
  }
};

exports.edge91BetaBrowserPreference = '91.0 beta';

exports.dnsCaching = {
  hoothootKey: 'automate_dns_metric'
};

exports.shouldPersistKeyObjectFlagPrefix = "SHOULD_PERSIST_HA_";

exports.monitoringDowntimeStates = {
  runningState: 'runningSessions',
  forcedDowntimeState: 'forcedDowntime',
  firstNon200State: 'firstNon200',
};

exports.alertReceivers = {
  automate: ['automate'],
  appAutomate: ['automate', 'app-automate'],
};

// Constants related to monitoring service
exports.monitoring = {
  oneMinute: 60 * 1000,
  logsPath: '/ebs/',
  redisWatchKey: 'monitoringCurrentMaster', // Key to watch for becomeMaster scripts
  ackLimitForMaster: 60 * 1000, // 1 minute
  pingForMasterInterval: 10 * 1000, // 10 seconds
  thresholdForPipelineRestore: 10 * 60 * 1000, // 10 minutes
  thresholdForHardPipelineRestore: 2 * 60 * 60 * 1000, // 2 hours
  reqToTimeKeyAppAutomate: 'requestToTimeAppAutomate',
  reqToTimeKeyAutomate: 'requestToTimeAutomate',
  hoothootRequestQueueThreshold: 960 * 1000, // 16 mins
  queueStatsKeyHoothoot: 'pending_queue_stats',
  pending_queue_stats_threshold: 10,
  tasksetSizeAlertThreshold: 10, // % value for alerting
  cumulativeEventKeyHoothoot: 'cumulative_event_data',
  ensureOncePerDayKey: 'monitor::setOncePerDay', // redis key which ensures that it set only once per day
  verifyRedisAliveKey: 'monitor::redisConnectivity',
  pipelineState : {
    restored: 0,
    penalized: 1,
    unrestored: 2
  },
  downtimeStates: Object.freeze({
    [ exports.monitoringDowntimeStates.runningState ]: {
      redisValue: 'running',
      humanDescription: 'Sessions running in normal state',
      maxRequestsValue: 2,
    },
    [ exports.monitoringDowntimeStates.forcedDowntimeState ]: {
      redisValue: 'forcedZero',
      humanDescription: 'downtime - forced to 0',
      maxRequestsValue: 0,
    },
    [ exports.monitoringDowntimeStates.firstNon200State ]: {
      redisValue: 'waitNon200',
      humanDescription: 'downtime - waiting for 1 non200 response', // transitions to forcedDowntime on first non200
      maxRequestsValue: null,
    },
  }),
};

exports.defaultAppiumVersionIOS = '1.7.0';
exports.chrome_har_capturer_server_port = 32349;
exports.chrome_har_capturer_server_port_v2 = 32348;
exports.chrome_har_capturer_server_port_v1 = 32347;
exports.skip_delete_jar_versions = ["3.0.0-beta1", "3.0.0-beta2", "3.0.0-beta4", "3.0.0", "3.0.1", "3.1.0", "3.2.0", "3.3.0", "3.3.1", "3.4.0"];
exports.preventPostWindowDeleteCommandsIOS = ['1.6.5', '1.7.0', '1.7.1', '1.7.2', '1.8.0', '1.9.1', '1.10.1', '1.11.1', '1.12.1', '1.13.0', '1.14.0', '1.15.0'];

// Custom messages for firecmd mapping for kind and messages
exports.firecmd_custom_exceptions = {
  "app_download_install_failure": "Could not start a session. Something went wrong with app launch. Please try to run the test again.",
  "apks_download_install_failure": "[BROWSERSTACK_BUNDLETOOL_APP_INSTALLATION_FAILED]",
  "other_apps_install_failure": "Could not start a session. Something went wrong with app launch. Please try to run the test again.",
  "mid_session_apps_download_failure": "Could not start a session. Something went wrong with app launch. Please try to run the test again.",
  "custom_media_download_update_failed": "Could not start a session. Something went wrong with uploaded media on device. Please try to run the test again.",
  "google_login_failure": "Could not start a session : Something went wrong with Google login. Please try to run the test again.",
  "google-play-store-login-incorrect-credentials" :  "Could not start a session : Incorrect google credentials.",
  "google-play-store-login-captcha" :  "Could not start a session : Captcha prompted during Google login.",
  "google-play-store-verification" :  "Could not start a session : Verification prompted during Google login.",
  "google-play-store-password-change-required" :  "Could not start a session : Password change prompted during Google login.",
  "google-play-store-timeout" :  "Could not start a session : Something went wrong with Google login. Please try to run the test again.",
  "google-play-store-locate-signin" :  "Could not start a session : Something went wrong with Google login. Please try to run the test again.",
  "google-play-store-password-again" :  "Could not start a session : Something went wrong with Google login. Please try to run the test again.",
  "google-play-store-login" :  "Could not start a session : Something went wrong with Google login. Please try to run the test again.",
  "google-account-requires-2fa" : "Could not start this session due to 2-factor authentication prompt in Google Login. Please disable 2-factor authentication or pass different account credentials in browserstack.appStoreConfiguration capability",
  "appium_startup_failure": "Could not start a session : Something went wrong with Appium server. Please try to run the test again.",
  "local_setup_failed": "Could not start a session : Something went wrong with local testing. Please try to run the test again.",
  "enterprise_app_trust_failed": "Could not start a session : Something went wrong with app launch. Please try to run the test again.",
  "airplane_mode_set_failed": "Could not start a session : Something went wrong enabling airplane mode. Please try to run the test again.",
  "app_certificate_parse_failure": "Could not start a session. Something went wrong during app installation. Please build the app with correct certificates & run the test again.",
  "mitm_setup_failed": "Could not start a session. Something went wrong with app launch. Please try to run the test again.",
  "app_settings_invalid_key": "Could not start a session : '<key>' setting in App Settings DSL does not exist in App's Settings Bundle. Please check the DSL and try again. If the error persists, please reach out to support.",
  "app_settings_invalid_value": "Could not start a session : '<value>' value for '<key>' setting in App Settings DSL is not a valid value as per App's Settings Bundle. Please check the DSL and try again. If the error persists, please reach out to support.",
  "app_settings_unsupported_specifier": "Could not update app settings as the key '<key>' is of specifier type '<type>' which is not supported currently. Please remove this setting from the App Settings DSL & retry. For more details, please check BrowserStack documentation [https://www.browserstack.com/docs/app-automate/appium/advanced-features/ios-app-settings] & Apple documentation Implementing an iOS Settings Bundle. If the error persists, please reach out to support.",
  "app_settings_setting_automation_error": "Could not start a session : Failed to update '<key>' Setting due to an internal error. Please try again and if the error persists, please reach out to support.",
  "app_settings_internal_error": "Could not start a session : We are facing some issues due to an internal error. Please try again and if the error persists, please reach out to support.",
  "setting_timezone_failed": "[BROWSERSTACK_TIMEZONE_UPDATE_FAILED] 'Something went wrong while setting the device timezone. Please retry and if the error persists, refer to our documentation or reach out to support.'",
  "unknown_exception": "Could not start a session. Something went wrong with app launch. Please try to run the test again.",
  "unhandled_exception": "Could not start a session. Something went wrong with app launch. Please try to run the test again.",
  "unhandled_response": "Could not start a session. Something went wrong with app launch. Please try to run the test again.",
  "set_passcode_failed": "[BROWSERSTACK_DEVICE_PASSCODE_SETUP_FAILED] Could not start the session due to an internal error. Please retry and if the error persists, reach out to support",
  "device_preferences_setup_failed_adb": "[BROWSERSTACK_ADB_COMMAND_EXECUTION_ERROR]",
  "device_preferences_setup_failed_internal": "[BROWSERSTACK_INTERNAL_ERROR] Could not execute the ADB command due to an internal error. Please retry and if the error persists, reach out to support.",
  "load_custom_contacts_failed": "[BROWSERSTACK_CONTACTS_ADDITION_FAILED] Contacts from the vcf file could not be added on the device.",
  "custom_contacts_preload_failed": "[BROWSERSTACK_CONTACTS_LOAD_FAILED] Could not start the session due to an internal error. Please retry and if the error persists, reach out to support",
  "audio_injection_setup_failed": '[BROWSERSTACK_AUDIO_INJECTION_SETUP_FAILED] Could not start the session with audio injection capability due to an internal issue/error. For more details, please reach out to support. ',
  "update_ios_device_settings_failed": '[BROWSERSTACK_UPDATE_IOS_DEVICE_SETTINGS_FAILED] Could not start the session with Updated iOS Device Settings due to an internal issue/error. For more details, please reach out to support. ',
  "biometric_injection_failure_as_proguarded": '[BROWSERSTACK_APP_TYPE_NOT_SUPPORTED] Currently, we do not support the uploaded app type with our Biometric Authentication feature. For more details, please refer to our documentation or reach out to our support team.',
  "camera_injection_failure_as_proguarded": '[BROWSERSTACK_APP_TYPE_NOT_SUPPORTED] Currently, we do not support the uploaded app type with our Camera Image Injection feature. For more details, please refer to our documentation or reach out to our support team.',
  "apple_pay_setup_failed": '[BROWSERSTACK_APPLE_PAY_SETUP_FAILED] Could not start the session with Apple Pay functionality due to an internal issue/error. Please try again. If error persists, please reach out to support.',
  "dac_download_and_install_app": 'Could not start a session. Something went wrong with app launch. Please try to run the test again.',
  "detox_startup_failure": 'Could not start a session : Something went wrong with Detox server. Please try to run the test again.',
  "browserstack_certificate_install_failure": '[BROWSERSTACK_CERTIFICATE_INSTALL_FAILURE] Certificate could not be installed successfully, please try again.',
  "certificate_install_invalid_password": '[BROWSERSTACK_CERTIFICATE_INSTALL_FAILURE] Certificate could not be installed successfully because of incorrect password or corrupted certificate, please try again.'
};

exports.firecmd_app_installation_kinds = ["app_download_install_failure", "other_apps_install_failure", "app_certificate_parse_failure", "apks_download_install_failure"];

exports.exceptions_needing_templating = ["app_settings_invalid_key", "app_settings_invalid_value", "app_settings_setting_automation_error", "app_settings_unsupported_specifier"];

// Google playstore user related login error kinds
exports.googleLoginUserErrors = ['google-play-store-login-incorrect-credentials', 'google-play-store-login-captcha', 'google-play-store-verification', 'google-play-store-password-change-required', 'google-account-requires-2fa'];

exports.customerIssuesRegex = [
  /processarguments must be a json/,
  /no such context found/,
  /it exists and is a launchable activity/,
  /never started/,
  /set the orientation, but app refused to rotate/,
  /the desiredcapabilities object was not valid for the following reason\(s\): .* must be of type string/,
  /unexpected token .* in json at position .*|setting .* is not supported. only the following settings are supported.*/,
  /make sure the 'internet' permission is requested in the android manifest of your application under test/,
  /espresso server process has been unexpectedly terminated./
];

// Kafka related constants
exports.kafkaConfig = configFile.kafkaConfig;
exports.KafkaRequestTimeout = configFile.kafkaConfig.request_timeout || 30000; // Default request timeout is 30 secs
exports.kafkaMetaDataRefreshInterval = 5000; // 5 seconds
exports.kafkaConnectedBrokersCheckInterval = 30000; // 30 seconds
exports.ALL_KAFKA_TOPICS = [configFile.kafkaConfig.raw_logs_topic, configFile.kafkaConfig.raw_extended_duration_logs_topic, configFile.kafkaConfig.instrumentation_logs_topic, configFile.kafkaConfig.console_logs_topic, configFile.kafkaConfig.performance_logs_topic];

exports.PII_CAPABILITIES = ['user', 'url', 'browserstack.user', 'browserstack.key', 'browserstack.tunnelIdentifier', 'browserstack.localIdentifier', 'browserstack.url', 'key', 'browserstack.username', 'browserstack.userName', 'browserstack.accessKey'];
exports.BSTACK_OPTIONS_PII_CAPABILITIES = ['user', 'url', 'key', 'tunnelIdentifier', 'localIdentifier', 'userName', 'accessKey'];
exports.BROWSER_OPTIONS = ['ChromeOptions', 'chromeOptions', 'goog:chromeOptions', 'ms:edgeOptions', 'EdgeOptions', 'edgeOptions', 'firefoxProfile', 'firefox_profile', 'moz:firefoxOptions'];

exports.USER_TERMINATED_SESSIONS = 'USER_TERMINATED_SESSIONS';
exports.BROWSERSTACK_EXECUTOR_PREFIX = 'browserstack_executor:';
exports.MAX_SELENIUM_COMMANDS_INSTRUMENTATION = 3000;

exports.RAW_LOGS_SUFFIX = 'raw_logs';
exports.RAW_LOGS_EXTENDED_DURATION_SUFFIX = 'raw_extended_duration_logs';
exports.CONSOLE_LOGS_SUFFIX = 'console_logs';
exports.PERFORMANCE_LOGS_SUFFIX = 'performance_logs';
exports.HUB_INSTRUMENTATION_LOGS_SUFFIX = 'instrumentation_logs';
exports.KAFKA_S3_LOGS_REGEX = `(${this.RAW_LOGS_SUFFIX}|${this.CONSOLE_LOGS_SUFFIX}|${this.RAW_LOGS_EXTENDED_DURATION_SUFFIX})`;
exports.KAFKA_EDS_LOGS_REGEX = `(${this.PERFORMANCE_LOGS_SUFFIX})`;
exports.HUB_INSTRUMENTATION_LOGS_REGEX = `(${this.HUB_INSTRUMENTATION_LOGS_SUFFIX})`;
exports.KAFKA_S3_REGEX_OBJ = new RegExp(this.KAFKA_S3_LOGS_REGEX, 'im');
exports.KAFKA_EDS_LOGS_OBJ = new RegExp(this.KAFKA_EDS_LOGS_REGEX, 'im');
exports.HUB_INSTRUMENTATION_LOGS_OBJ = new RegExp(this.HUB_INSTRUMENTATION_LOGS_REGEX, 'im');
exports.KAFKA_S3_UPLOAD_RETRIES = 12;

exports.KAFKA_LOG_PARSING_ERROR = 'kafka-log-parsing-error';
exports.KAFKA_LOG_FETCHING_ERROR = 'kafka-log-fetching-error';
exports.KAFKA_LOG_PARSING_ERROR_RETRIEVE = 'kafka-log-parsing-error-retrieve';
// consts related to caps being sent to RailsApp
exports.blacklistedCaps = ['specs', 'exclude'];
exports.railsDeleteKeys = [];
exports.railsDeleteLength = 50000;
exports.railsPerCapMaxLength = 10000;
exports.railsDeleteCapsMethodMaxDepth = 15;

exports.PUT = "PUT";
exports.GET = "GET";
exports.AUTH_PREFIX = "Basic ";
exports.SELENIUM_ERROR_CODE = 13;
exports.SELENIUM_SUCCESS_CODE = 0;
exports.EDS_KIND_AUTOMATION_SESSION_STATS = "automation_session_stats";
exports.EDS_KIND_APP_AUTOMATION_SESSION_STATS = "app_automation_session_stats";
exports.HOOTHOOT_DEFAULT_USER = 'hoothoot_default_user';
exports.BSTACK_HOST_HEADER = 'BStack-Host';
exports.BSTACK_ENV_HEADER = 'X-Source-Env-Type';
exports.BSTACK_HOST_HEADER_MISSING_ERR = `Hub to terminal HTTP request missing header: ${exports.BSTACK_HOST_HEADER}`;

exports.DETOX = "detox";

// consts related to order of chrome options for chrome based browsers
exports.CHROME_OPTIONS_PREFERENCE = ["goog:chromeOptions", "chromeOptions", "ms:edgeOptions", "edgeOptions"];

exports.HUB_TO_BROWSERSTACK_RETRY = {
  default: {
    __COMMENT__: "This retry methodology includes requests that will be retry following exponention backoff algorithm.",
    __RESPONSE_TYPES__: ["socket hang up", "connect EHOSTUNREACH", "connect ETIMEDOUT", "getaddrinfo EAI_AGAIN", "BS Response 502"],
    maxRetryCount: 6,
    maxRequestLife: 300000
  },
  lateRetry: {
    __COMMENT__: "This retry methodology includes requests that will be retry a little late with four different retry attempts.",
    __RESPONSE_TYPES__: ["BS Response 503", "BS Response 504", "BS Response 404"],
    maxRetryCount: 5,
    maxRequestLife: 900000
  },
};


exports.NUMBER_OF_RETRIES = 6;
exports.EXPONENTIAL_BACKOFF_DELAY_TIME = 5000;
exports.LATE_RETRY_DELAY = exports.isTestingEnv ? [0, 0, 0, 0, 0] : [0, 360000, 240000, 120000, 120000];
const generateDefaultRetryDelayArray = () => {
  let retryDelay = [0];
  for (let i = 1; i < exports.NUMBER_OF_RETRIES; i++) {
    retryDelay.push(Math.pow(2, i - 1) * exports.EXPONENTIAL_BACKOFF_DELAY_TIME);
  }
  return retryDelay;
};

exports.generateDefaultRetryDelayArray = generateDefaultRetryDelayArray;

exports.DEFAULT_RETRY_DELAY = exports.isTestingEnv ? [0, 0, 0, 0, 0] : generateDefaultRetryDelayArray();

exports.DEFAULT_BACKUP_DELAY = exports.isTestingEnv ? 0 : 5000;

exports.LATE_RETRY = 'lateRetry';
exports.DEFAULT_RETRY = 'default';

exports.DEFAULT_HUB_TO_BROWSERSTACK_RETRY_METHOD = 'default';

exports.nonPrefixKafkaEnvironments = ['local', 'production'];

exports.DEFAULT_RESOLUTION_WIDTH = '1920';
exports.DEFAULT_RESOLUTION_HEIGHT = '1080';

exports.fileSuffixForTopicV2 = {
  raw_logs: '-logs-v2.txt',
  raw_extended_duration_logs: '-logs-v2.txt',
  console_logs: '-console-logs-v2.txt',
  performance_logs: '-performance-logs-v2.txt',
  instrumentation_logs: '-instrumentation-logs-v2.txt'
};

exports.logTypeToFeatureUsageKafkaMap = {
  'raw_logs': 'kafkaRawLogs',
  'raw_extended_duration_logs': 'kafkaRawLogs',
  'console_logs': 'kafkaConsoleLogs',
  'performance_logs': 'kafkaPerformanceLogs'
};

exports.NGINX_SOURCE_FROM_CODES = {
  JAR: '02',
  HUB: '01',
};

exports.CAPABILITIES = 'capabilities';
exports.DESIRED_CAPABILTITES = 'desiredCapabilities';
exports.BSTACK_OPTIONS = 'bstack:options';

exports.OS_VERSION_MAP = {
  winxp: 'XP',
  win7: '7',
  win8: '8',
  'win8.1': '8.1',
  win10: '10',
  win11: '11',
  macsqa: "Sequoia",
  macson: "Sonoma",
  macven: "Ventura",
  macmty: "Monterey",
  macbsr: "Big Sur",
  maccat: "Catalina",
  macmo: "Mojave",
  machs: "High Sierra",
  macsie: "Sierra",
  macelc: "El Capitan",
  macyos: "Yosemite",
  macmav: "Mavericks",
  macml: "Mountain Lion",
  maclion: "Lion",
  macsl: "Snow Leopard"
};

exports.AUTOMATION_NAMES_NOT_SUPPORTING_W3C = ["youiengine"];

exports.SO_TIMEOUT_BUCKET = {
  stagingWebsite: 'SOTIMEOUT-browserstack-client_staging_website',
  deleteWindow: 'SOTIMEOUT-customer-delete_window',
  nonNetworkCommand: 'SOTIMEOUT-customer-non_network_command',
  basicAuth: 'SOTIMEOUT-customer-basic_auth',
  basicAuthSafari: 'SOTIMEOUT-customer-basic_auth_safari'
};

exports.PURE_NETWORK_COMMANDS = ['POST:url', 'POST:refresh', 'POST:back'];
exports.PURE_NON_NETWORK_COMMANDS = ['GET:cookie', 'POST:element', 'POST:elements', 'GET:url', 'POST:moveto', 'POST:window', 'GET:screenshot', 'DELETE:window', 'POST:cookie', 'POST:maximize', 'POST:size', 'GET:display', 'POST:frame', 'GET:alert_text', 'POST:parent', 'POST:context', 'DELETE:cookie', 'GET:size', 'GET:handles', 'GET:text', 'GET:window', 'GET:enabled', 'GET:contexts', 'GET:location', 'GET:window_handle', 'GET:window_handles'];
exports.DELETE_WINDOW_COMMANDS = ['DELETE:window'];
exports.BASIC_AUTH_COMMANDS = ['POST:url'];

exports.NETWORK_STAGING_URL_REGEX = [
  /^.*:\/\/next\.airbnb\./, //specific airbnb case
  /^.*:.*(\.|-|\/\/)(qa|uat|nonprod|staging|preprod|stage|dev|demo|uat2|preview|sits|ssr|stg|master|testing|develop|regression)(\.|-).*/, // for urls like http(s)://qa.random.com, http(s)://qa-xyz.random.com, http(s)://xyz-qa.random.com, http(s)://www.qa.random.com
  /^.*:\/\/localhost:\d+\//, // for urls like http://localhost:3000/
  /^.*:\/\/bs-local\./, // for urls like http://bs-local.com
  /^.*:\/\/.*:\d+\//, // for urls like http://www.random:3000.com
];
exports.BASIC_AUTH_URL_REGEX = [/^.*:\/\/.*:.*@.*\./];
exports.START_REQUEST_URL_REGEX = /^\/wd\/hub\/session$/;
exports.STOP_REQUEST_URL_REGEX = /\/wd\/hub\/session\/[^/]*$/;

exports.DURATION_MAP_REGEX = /(rails-defender|selauth-request|check-rails-response-code|fire-command|patch-start-session|time-in-queue|start-command)/;

// Static responses for commands not supported on selenium but on appium
exports.UNHANDLED_SELENIUM_COMMAND_RESPONSE = {
  desktopOrientation: 'LANDSCAPE'
};

// Alongwith these caps, all the caps prefixed with "browserstack." are also app automate caps
// No new such capability will be introduced from now onwards
exports.APP_AUTOMATE_SPECIFIC_CAPS = [
  'os',
  'os_version',
  'device',
  'project',
  'build',
  'name',
  'deviceOrientation',
  'disableAnimations',
  'app_url',
  'appUrl'
];

exports.DEFAULT_IOS_MASKABLE_COMMANDS = [
  'ios_getsession'
];

exports.FIRECMD_USER_ERROR_STRING = 'user_error';
// This needs to be slightly lesser than 900 seconds(15 minutes) to avoid any unknown race conditions.
exports.MAX_FIRECMD_TIMEOUT = 840;
exports.GEOCOMPLY_FIRECMD_TIMEOUT = 75000; // in ms
exports.BROWSER_PROFILING_FREQ = 3; // in seconds

exports.APPIUM_START_ERROR_RETRY_CASES = ['Unable to launch WebDriverAgent because of xcodebuild failure: xcodebuild failed with code 65', 'Unable to launch WebDriverAgent because of xcodebuild failure: xcodebuild failed with code 70', 'Original error: Could not find a connected Android device', 'Original error: The instrumentation process cannot be initialized', 'was not in the list of connected devices', 'Original error: Could not proxy command to remote server. Original error: Error: socket hang up', 'Original error: listen EADDRINUSE: address already in use', 'A new session could not be created. Details: java.lang.IllegalStateException: UiAutomation not connected!', 'A new session could not be created. Details: java.lang.IllegalStateException: Cannot call disconnect', 'Can\'t find service: settings', 'The port #8100 is occupied by an other process', 'adb: error: cannot bind listener: Address already in use', "Try to increase the [0-9]*ms adb execution timeout represented by 'adbExecTimeout' capability", "Command output: error: device '[0-9A-Za-z]*' not found"];

exports.MID_SESSION_BROWSERSTACK_ERROR_BUCKET = {
  econnrefused: 'mid_session_econnrefused'
};

exports.MID_SESSION_BROWSERSTACK_ERROR_RAWLOGS_MESSAGE = {
  'mid_session_econnrefused' : 'BROWSERSTACK_DEVICE_UNREACHABLE'
};

exports.ELEMENT_NOT_FOUND_DETECTED = 'element_not_found_detected';
exports.PAGE_LOAD_ERROR_DETECTED = 'page_load_error_detected';

/*
 JSE Error cases
 Format - error name: {code:<code for BQ>, message: <message sent to customer>}
*/
exports.JSE_EXECUTERTYPE_REGEX = new RegExp('\\[executorType\\]', 'g');
exports.JSE_GENERIC_ERRORS = {
  'generic_error': { code: 'generic_error', message: 'Something went wrong' },
  'invalid_action': { code: 'unsupported_action', message: 'Invalid action [executorType]' },
  'smart_tv_not_supported': { code: 'smart_tv_not_supported', message: 'Error executing browserstack_executor command. [executorType] is not supported for Smart TV.'},
};

//Pageload timeout errors
exports.PAGE_LOAD_ERROR_REGEX = new RegExp("timeout: cannot determine loading status|timeout: Timed out receiving message from renderer|Error loading page, timed out", 'im');

exports.CUSTOM_EXECUTOR_ERRORS_ACCEPT_SSL = {
  'unsupported_on_ios10_or_below': { code: 'unsupported_on_ios10_or_below', message: 'Specified action is supported only on iOS 11 and above.'},
  'unsupported_browser': { code: 'unsupported_browser', message: 'Specified browser is not supported for accept SSL' },
};

// File executors
exports.JSE_FILE_EXECUTORS_GENERIC = {
  'supports_only_desktop': { code: 'supports_only_desktop', message: '[executorType] is supported only for desktop browsers'},
  'generic_error': { code: 'generic_error', message: 'Something went wrong' },
  'file_not_found': { code: 'file_not_found', message: 'File not found' },
  'invalid_action': { code: 'unsupported_action', message: 'Invalid action' },
  'supports_only_windows': { code: 'supports_only_windows', message: '[executorType] is supported only for Windows 8 and above' },
  'supports_only_chromium': { cide: 'supports_only_chromium', message: '[executorType] is supported only for Chrome and Edge browsers' }
};

exports.JSE_SAVE_FILE = {
  'supports_only_ie': { code: 'supports_only_ie', message: 'This command is only supported on internet explorer' },
  'autoit_generic_error': { code: 'autoit_generic_error', message: 'Something went wrong' },
};

exports.JSE_BASIC_AUTH = {
  'unsupported_action': { code: 'unsupported_action', message: 'Action [executorType] is only supported on Windows, Mac and iOS' },
  'unsupported_browsers': { code: 'unsupported_browsers', message: 'Action [executorType] is only supported on Google Chrome, Mozilla Firefox, Microsoft Edge & IE' },
  'missing_arguments': { code: 'missing_arguments', message: 'Action [executorType] requires arguments to be present' },
};

exports.JSE_SESSION_STATUS = {
  'missing_status': { code: 'missing_status', message: 'Missing status' },
  'generic_error': { code: 'generic_error', message: 'Something went wrong' },
};

// AA JSE Error cases
exports.BIOMETRIC_CUSTOM_EXECUTOR_ERRORS = {
  'invalid_action_name': { code: 'invalid_action_name', message: "[BROWSERSTACK_INVALID_ACTION_NAME] 'biometric' action is not supported on Automate. For details on supported actions, refer to our documentation."},
  'invalid_action_used': { code: 'invalid_action_used', message: "[BROWSERSTACK_INVALID_ACTION_USED] 'To use biometric authentication in any session you have to send browserstack.enableBiometric capability as true in desired Appium capabilities. For details, refer to our documentation."},
  'invalid_arg_passed': { code: 'invalid_argument_passed', message: "[BROWSERSTACK_INVALID_ARGUMENTS_PASSED] Arguments passed are invalid. For details on supported arguments, refer to our documentation or reach out to support."},
  'popup_absent': { code: 'popup_absent', message: "[BROWSERSTACK_COMMAND_EXECUTION_FAILED] Something went wrong while executing the command. Please retry and if the error persists, refer to our documentation or reach out to support."},
  'popup_not_clicked': { code: 'popup_not_clicked', message: "[BROWSERSTACK_COMMAND_EXECUTION_FAILED] Something went wrong while executing the command. Please retry and if the error persists, refer to our documentation or reach out to support."},
  'unknown': { code: 'unknown', message: "[BROWSERSTACK_COMMAND_EXECUTION_FAILED] Something went wrong while executing the command. Please retry and if the error persists, refer to our documentation or reach out to support."},
};


exports.AUDIO_INJECTION_CUSTOM_EXECUTOR_ERRORS = {
  invalid_action_used: { code: 'caps_not_passed', message: '[BROWSERSTACK_INVALID_ACTION_USED] To use audio injection in any session you have to send browserstack.enableAudioInjection capability as true in desired Appium capabilities. For details, refer our documentation.' },
  invalid_media_url: { code: 'invalid_audio_url', message: '[BROWSERSTACK_INVALID_AUDIO_URL_ERROR] Invalid value passed for audioUrl key.  Please pass a valid audioUrl. For details, please refer our documentation or reach out to support.' },
  invalid_platform: { code: 'invalid_platform', message: '[BROWSERSTACK_INCOMPATIBLE_CAPABILITIES_PASSED] \'audio injection\' action is supported only on Android. For details on supported actions, refer to our documentation.' },
  platform_not_supported: { code: 'invalid_action_used', message: '[BROWSERSTACK_INVALID_ACTION_USED] [executorType] executor command is not a valid command for Audio Injection on this platform. Please refer to our documentation for supported commands, else reach out to support.' },
  invalid_argument_passed: { code: 'invalid_argument_passed', message: '[BROWSERSTACK_INVALID_CAPABILITY_ERROR] ] Arguments passed are invalid. For details on supported arguments, refer to our documentation or reach out to support.' },
  invalid_media_type_aa: { code: 'unsupported_audio_format', message: '[BROWSERSTACK_INVALID_MEDIA_TYPE_PASSED] Please provide a valid audio type. Audio Injection only allows audio of type \'.mp3\', \'.wav\',\'.aac\', \'.ogg\', \'wma\'. For more details, refer to our documentation.' },
  invalid_media_type_aut: { code: 'unsupported_audio_format', message: '[BROWSERSTACK_INVALID_MEDIA_TYPE_PASSED] Please provide a valid audio type. Audio Injection only allows audio of type \'.mp3\', \'.wav\'. For more details, refer to our documentation.' },
  audio_injection_failure: { code: 'executor_internal_error', message: '[BROWSERSTACK_AUDIO_EXECUTOR_ERROR] We could not execute the injectAudio executor command successfully due to an internal issue/error. For more details, please reach out to support.' },
  audio_start_failure: { code: 'executor_internal_error', message: '[BROWSERSTACK_AUDIO_EXECUTOR_ERROR] We could not execute the startAudio executor command successfully due to an internal issue' },
  audio_stop_failure: { code: 'executor_internal_error', message: '[BROWSERSTACK_AUDIO_EXECUTOR_ERROR] We could not execute the stopAudio executor command successfully due to an internal issue' },
  audio_start_custom_failure: { code: 'inject_audio_not_called', message: '[BROWSERSTACK_INVALID_ACTION_USED] Please run the injectAudio executor command with a valid audioUrl value before calling the startAudio executor command. For more details, please refer our documentation or reach out to support.' },
  audio_stop_custom_failure: { code: 'start_audio_not_called', message: '[BROWSERSTACK_INVALID_ACTION_USED] Please run the startAudio executor command before calling the stopAudio executor command. For more details, please refer our documentation or reach out to support.' },
  unknown: { code: 'unknown', message: '[BROWSERSTACK_COMMAND_EXECUTION_FAILED] Something went wrong while executing the command. Please retry and if the error persists, refer to our documentation or reach out to support.'}
};

exports.IMAGE_INJECTION_CUSTOM_EXECUTOR_ERRORS = {
  'invalid_action_name': { code: 'invalid_action_name', message: "[BROWSERSTACK_INVALID_ACTION_NAME] 'cameraImageInjection' action is not supported on Automate. For details on supported actions, refer to our documentation."},
  'invalid_action_used': { code: 'invalid_action_used', message: "[BROWSERSTACK_INVALID_ACTION_USED] 'To use camera image injection in any session you have to send browserstack.enableCameraImageInjection capability as true in desired Appium capabilities. For details, refer to our documentation."},
  'invalid_argument_passed': { code: 'invalid_argument_passed', message: "[BROWSERSTACK_INVALID_ARGUMENTS_PASSED] Arguments passed are invalid. For details on supported arguments, refer to our documentation or reach out to support."},
  'invalid_image_url': { code: 'invalid_image_url', message: "[BROWSERSTACK_INVALID_IMAGE_URL_PASSED] Please pass a valid image URL. For details on how to upload image, refer to our documentation - [https://www.browserstack.com/docs/app-automate/api-reference/media/files#upload-media-file]"},
  'invalid_image_type': { code: 'invalid_image_type', message: "[BROWSERSTACK_INVALID_IMAGE_TYPE_PASSED] Please provide a valid image type. Camera Image Injection only allows images of type '.jpg', '.jpeg', or '.png'. For more details, refer to our documentation."},
  'unknown': { code: 'unknown', message: "[BROWSERSTACK_COMMAND_EXECUTION_FAILED] Something went wrong while executing the command. Please retry and if the error persists, refer to our documentation or reach out to support."},
};

exports.VIDEO_INJECTION_CUSTOM_EXECUTOR_ERRORS = {
  'invalid_action_name': { code: 'invalid_action_name', message: "[BROWSERSTACK_INVALID_ACTION_NAME] 'cameraVideoInjection' action is not supported on Automate. For details on supported actions, refer to our documentation."},
  'invalid_action_used': { code: 'invalid_action_used', message: "[BROWSERSTACK_INVALID_ACTION_USED] 'To use camera video injection in any session you have to send browserstack.enableCameraVideoInjection capability as true in desired Appium capabilities. For details, refer to our documentation."},
  'invalid_argument_passed': { code: 'invalid_argument_passed', message: "[BROWSERSTACK_INVALID_ARGUMENTS_PASSED] Arguments passed are invalid. For details on supported arguments, refer to our documentation or reach out to support."},
  'invalid_media_url': { code: 'invalid_media_url', message: "[BROWSERSTACK_INVALID_VIDEO_URL_PASSED] Please pass a valid video URL. For details on how to upload video, refer to our documentation - [https://www.browserstack.com/docs/app-automate/api-reference/media/files#upload-media-file]"},
  'invalid_image_type': { code: 'invalid_video_type', message: "[BROWSERSTACK_INVALID_VIDEO_TYPE_PASSED] Please provide a valid video type. Camera Video Injection only allows images of type '.mp4'. For more details, refer to our documentation."},
  'unknown': { code: 'unknown', message: "[BROWSERSTACK_COMMAND_EXECUTION_FAILED] Something went wrong while executing the command. Please retry and if the error persists, refer to our documentation or reach out to support."},
};

exports.APPLE_PAY_CUSTOM_EXECUTOR_ERRORS = {
  'caps_not_passed': { code: 'caps_not_passed', message: "[BROWSERSTACK_INVALID_ACTION_USED] To use apple pay, you need to set browserstack.enableApplePay as true in the desired capabilities for the appium session. For details, refer our documentation."},
  'invalid_value': { code: 'invalid_value', message: "[BROWSERSTACK_INVALID_ARGUMENT_ERROR] Invalid value passed for confirmPayment argument in the Apple Pay executor. Please pass a valid argument. For details, please refer to our documentation."},
  'invalid_args': { code: 'invalid_args', message: "[BROWSERSTACK_INVALID_ARGUMENT_ERROR] Invalid argument passed in applePay executor command. Please pass a valid argument and try again. For more details, please refer to our documentation."},
  'executor_internal_error': { code: 'executor_internal_error', message: "[BROWSERSTACK_APPLE_PAY_EXECUTOR_ERROR] We could not execute the <applePay | confirmPayment> executor command successfully due to an internal issue/error. For more details, please reach out to support."},
  'parallel_apple_pay_command_error':  { code: 'parallel_apple_pay_command_error', message: "[BROWSERSTACK_MULTIPLE_APPLE_PAY_COMMANDS_ERROR] Running multiple Apple Pay commands in parallel is currently not supported on BrowserStack devices. "},
  'unknown': { code: 'unknown', message: "[BROWSERSTACK_COMMAND_EXECUTION_FAILED] Something went wrong while executing the command. Please retry and if the error persists, refer to our documentation or reach out to support."},
};


exports.APPLE_PAY_PREFILL_DATA_SCHEMA = {
  billingDetails: {
    firstName: String,
    lastName: String,
    street: String,
    addressLine2: String,
    state: String,
    postalCode: String,
    postCode: String,
    zip: String,
    city: String,
    province: String,
    islandName: String,
    country: String
  },
  shippingDetails: {
    firstName: String,
    lastName: String,
    street: String,
    addressLine2: String,
    state: String,
    postalCode: String,
    postCode: String,
    zip: String,
    city: String,
    province: String,
    islandName: String,
    country: String
  },
  contact: {
    email: String,
    phone: String
  }
};

exports.APPLE_PAY_PREFILL_DATA_CUSTOM_EXECUTOR_ERRORS = {
  'caps_not_passed': { code: 'caps_not_passed', message: "[BROWSERSTACK_INVALID_ACTION_USED] To use apple pay, you need to set browserstack.enableApplePay as true in the desired capabilities for the appium session. For details, refer our documentation."},
  'internal_error': { code: 'internal_error', message: "[BROWSERSTACK_APPLE_PAY_EXECUTOR_ERROR] We could not execute the applePayDetails executor command successfully due to an internal issue/error. For more details, please reach out to support."},
  'parallel_apple_pay_commands_error':  { code: 'parallel_apple_pay_commands_error', message: "[BROWSERSTACK_MULTIPLE_APPLE_PAY_COMMANDS_ERROR] Running multiple applePayDetails commands in parallel is currently not supported on BrowserStack devices. For more details, please refer to our documentation."},
  'invalid_argument': { code: 'invalid_argument', message: "[BROWSERSTACK_INVALID_ARGUMENT_ERROR] Invalid argument passed in applePayDetails executor command. Please pass a valid argument and try again. For more details, please refer to our documentation."},
  'missing_argument': { code: 'missing_argument', message: "[BROWSERSTACK_NECESSARY_ARGUMENTS_ERROR] One or more necessary arguments are missing in the applePayDetails executor command. Please pass all required arguments and try again. For more details, please refer to our documentation."}
};

exports.ANDOID_DATE_TIME_CUSTOM_EXECUTOR_ERRORS = {
  'incompatible_product': { code: 'incompatible_product', message: "[BROWSERSTACK_UNSUPPORTED_PRODUCT] We do not support updateAndroidDeviceSettings command in Automate currently. For details on supported actions, please refer to our documentation ."},
  'incompatible_platform': { code: 'incompatible_platform', message: "[BROWSERSTACK_INCOMPATIBLE_COMMAND_PASSED] updateAndroidDeviceSettings command is not supported on iOS devices. For more details, please refer to our documentation ."},
  'incompatible_os_version':  { code: 'incompatible_os_version', message: "[BROWSERSTACK_INCOMPATIBLE_OS_VERSION] customDate | customTime | customDate and customTime command/s is/are supported only on Android 6 & above devices. For more details, please refer to our documentation."},
  'invalid_value': { code: 'invalid_value', message: "[BROWSERSTACK_INVALID_VALUE_PASSED] Invalid value passed in customDate | customTime | customDate and customTime command/s. Please pass a valid value and try again. For more details, please refer to our documentation."},
  'executor_internal_error': { code: 'executor_internal_error', message: "[BROWSERSTACK_UPDATE_ANDROID_SETTINGS_EXECUTOR_ERROR] We could not execute the customDate | customTime | 12HourTime | customDate and customTime command/s successfully due to an internal issue/error. For more details, please reach out to support."},
  'feature_not_available_in_current_plan_for_aa' : { code: 'feature_not_available_in_current_plan_for_aa', message: "[BROWSERSTACK_FEATURE_NOT_AVAILABLE_IN_CURRENT_PLAN] Setting device date & time functionality is only available with App Automate Device Cloud Pro plan. Subscribe to this plan by visiting our pricing page([https://www.browserstack.com/accounts/subscriptions?product=app-automate])."},
};

exports.ANDROID_HOUR_FORMAT_CUSTOM_EXECUTOR_ERRORS = {
  'incompatible_product': { code: 'incompatible_product', message: "[BROWSERSTACK_UNSUPPORTED_PRODUCT] We do not support updateAndroidDeviceSettings command in Automate currently. For details on supported actions, please refer to our documentation ."},
  'incompatible_platform': { code: 'incompatible_platform', message: "[BROWSERSTACK_INCOMPATIBLE_COMMAND_PASSED] updateAndroidDeviceSettings command is not supported on iOS devices. For more details, please refer to our documentation ."},
  'incompatible_os_version':  { code: 'incompatible_os_version', message: "[BROWSERSTACK_INCOMPATIBLE_OS_VERSION] 12HourTime command is supported only on Android 6 & above devices. For more details, please refer to our documentation."},
  'invalid_value': { code: 'invalid_value', message: "[BROWSERSTACK_INVALID_VALUE_PASSED] Invalid value passed in 12HourTime command. Please pass a valid value and try again. For more details, please refer to our documentation."},
  'executor_internal_error': { code: 'executor_internal_error', message: "[BROWSERSTACK_UPDATE_ANDROID_SETTINGS_EXECUTOR_ERROR] We could not execute the updateAndroidDeviceSettings command successfully due to an internal error. Please try again. For more details, please reach out to support."},
  'feature_not_available_in_current_plan_for_aa' : { code: 'feature_not_available_in_current_plan_for_aa', message: "[BROWSERSTACK_FEATURE_NOT_AVAILABLE_IN_CURRENT_PLAN] Setting device hour format functionality is only available with App Automate Device Cloud Pro plan. Subscribe to this plan by visiting our pricing page([https://www.browserstack.com/accounts/subscriptions?product=app-automate])."},
};

exports.UPDATE_ANDROID_SETTINGS_EXECUTOR_ERRORS = {
  'parallel_update_android_settings_command_error': { code: 'parallel_update_android_settings_command_error', message: '[BROWSERSTACK_MULTIPLE_ANDROID_SETTINGS_COMMANDS_ERROR] Running multiple updateAndroidDeviceSettings commands in parallel is currently not supported on BrowserStack devices. For more details, please refer to our documentation .' },
  'invalid_argument': { code: 'invalid_argument', message: '[BROWSERSTACK_INVALID_ARGUMENT_PASSED] Invalid argument passed in updateAndroidDeviceSettings executor command. Please pass a valid argument and try again. For more details, please refer to our documentation.' }

};

exports.SETTINGS_APP_CUSTOM_EXECUTOR_ERRORS = {
  'invalid_action_name': { code: 'invalid_action_name', message: "[BROWSERSTACK_INVALID_ACTION_NAME] 'updateAppSettings' action is not supported on Automate. For details on supported actions, refer to our documentation."},
  'invalid_argument_passed': { code: 'invalid_argument_passed', message: "[BROWSERSTACK_INVALID_ARGUMENTS_PASSED] Arguments passed are invalid. 'updateAppSettings' action expects a valid App Settings DSL. For details on the format, please refer our documentation. If the issue persists, please reach out to support."},
  'no_settings_bundle' : { code: 'no_settings_bundle', message: "[BROWSERSTACK_DSL_VALIDATION_FAILED] No Settings bundle exists in the app. Please check with your development team and try again. If the error persists, reach out to support."},
  'app_settings_device_location_off' : {code: 'device_location_off', message: "[BROWSERSTACK_INVALID_ACTION] iOS app settings for location cannot be modified when device location services have been turned OFF. Please refer to our documentation or reach out to support in case of any concerns."},
  'max_num_times' : { code: 'max_num_times', message: "[BROWSERSTACK_DSL_VALIDATION_FAILED] Maximum number of times UpdateAppSetting custom executor can be called in a session is 50. For details, please reach out to support."},
  'max_key' : { code: 'max_key', message: "[BROWSERSTACK_DSL_VALIDATION_FAILED] Maximum number of  keys allowed in App Settings DSL are 100. For details, please refer our documentation or reach out to support."},
  'app_settings_invalid_key': { code: 'invalid_key', message: "[BROWSERSTACK_DSL_VALIDATION_FAILED] '<key>' setting in App Settings DSL does not exist in App's Settings Bundle. Please check the DSL and try again. If the error persists, please reach out to support."},
  'app_settings_invalid_dsl': { code: 'invalid_dsl', message: "[BROWSERSTACK_INVALID_DSL_PASSED] App Settings JSON passed is not valid. For details on how to construct a valid DSL JSON please refer our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/ios-app-settings]). If the error persists, please reach out to support."},
  'app_settings_invalid_value': { code: 'invalid_value', message: "[BROWSERSTACK_DSL_VALIDATION_FAILED] '<value>' value for '<key>' setting in App Settings DSL is not a valid value as per App's Settings Bundle. Please check the DSL and try again. If the error persists, please reach out to support."},
  'app_settings_unsupported_specifier': { code: 'invalid_specifier', message:  "[BROWSERSTACK_DSL_VALIDATION_FAILED] Could not update app settings as the key '<key>' is of specifier type '<type>' which is not supported currently. Please remove this setting from the App Settings DSL & retry. For more details, please check BrowserStack documentation [https://www.browserstack.com/docs/app-automate/appium/advanced-features/ios-app-settings] & Apple documentation Implementing an iOS Settings Bundle. If the error persists, please reach out to support."},
  'app_settings_setting_automation_error': { code: 'setting_automation_error', message: "[BROWSERSTACK_APP_SETTINGS_UPDATE_FAILED] Failed to update '<key>' Setting due to an internal error. Please try again and if the error persists, please reach out to support."},
  'internal_error': { code: 'internal_error', message: "[BROWSERSTACK_INTERNAL_ERROR] We are facing some issues due to an internal error. Please try again and if the error persists, please reach out to support."},
  'invalid_os': {code: 'invalid_os', message: "[BROWSERSTACK_INCOMPATIBLE_OS] updateAppSettings custom executor is only supported for IOS devices."},
  'feature_not_available_in_current_plan_for_aa' : { code: 'feature_not_available_in_current_plan_for_aa', message: "[BROWSERSTACK_FEATURE_NOT_AVAILABLE_IN_CURRENT_PLAN] iOS App Settings capability was used in the test, but your account does not have access to this feature. You can try to run the test without this capability, or read out more about this at https://www.browserstack.com/docs/app-automate/appium/advanced-features/ios-app-settings"},
};

exports.DEVICE_INFO_CUSTOM_EXECUTOR_ERRORS = {
  'invalid_syntax': { code: 'invalid_syntax', message: "[BROWSERSTACK_INVALID_EXECUTOR_ERROR] Invalid syntax used in browserstack_executor to query SIM properties. Please use the correct syntax. For details, please refer to our documentation or reach out to support."},
  'caps_not_passed':  { code: 'caps_not_passed', message: "[BROWSERSTACK_INVALID_ACTION_USED] To use the deviceInfo executor command in order to query SIM properties in any session, you have to send browserstack.enableSim capability as true in desired Appium capabilities."},
  'executor_internal_error': { code: 'executor_internal_error', message: "[BROWSERSTACK_SIM_EXECUTOR_ERROR] We could not execute the deviceInfo executor command to query SIM properties successfully due to an internal issue/error. For more details, please reach out to support."},
};

exports.CUSTOM_GESTURES_CUSTOM_EXECUTOR_ERRORS = {
'incorrect_syntax': { code: 'incorrect_syntax', message: "[BROWSERSTACK_INVALID_VALUE] Invalid value passed for the deviceShake command. For the correct syntax, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/custom-gestures])."},
'executor_internal_error': { code: 'executor_internal_error', message: "[BROWSERSTACK_CUSTOM_GESTURE_EXECUTOR_ERROR] We could not execute the deviceShake command successfully due to an internal issue/error. For more details, please reach out to support."},
'incompatible_platform':  { code: 'incompatible_platform', message: "[BROWSERSTACK_INCOMPATIBLE_COMMAND_PASSED] deviceShake command is not supported on Android devices. For more details, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/custom-gestures])." },
'incompatible_os_version':  { code: 'incompatible_os_version', message: "[BROWSERSTACK_INCOMPATIBLE_OS_VERSION] Currently we support deviceShake command only on iOS 16 & above devices. For more details, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/custom-gestures])." },
'incompatible_product': { code: 'incompatible_product', message: "[BROWSERSTACK_UNSUPPORTED_PRODUCT] We do not support deviceShake in Automate currently. For details on supported actions, please refer to our documentation([https://www.browserstack.com/automate/capabilities])."},
'parallel_custom_gestures_command_error':  { code: 'parallel_custom_gestures_command_error', message: "[BROWSERSTACK_MULTIPLE_CUSTOM_GESTURES_COMMANDS_ERROR] Running multiple customGestures commands in parallel is currently not supported on BrowserStack devices. For more details, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/custom-gestures])."},
'feature_not_available_in_current_plan_for_aa' : { code: 'feature_not_available_in_current_plan_for_aa', message: "[BROWSERSTACK_FEATURE_NOT_AVAILABLE_IN_CURRENT_PLAN]  Device shake feature is only available with App Automate Device Cloud Pro plan. Subscribe to this plan by visiting our pricing page([https://www.browserstack.com/accounts/subscriptions?product=app-automate])."},
};

exports.DATE_TIME_CUSTOM_EXECUTOR_ERRORS = {
  'incompatible_os_version':  { code: 'incompatible_os_version', message: "[BROWSERSTACK_INCOMPATIBLE_OS_VERSION] Currently we support customTime command only on iOS 13 & above devices. For more details, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/date-time])."},
  'incompatible_custom_date_os_version':  { code: 'incompatible_os_version', message: "[BROWSERSTACK_INCOMPATIBLE_OS_VERSION] Currently we support customDate command only on iOS 16 and above devices. For more details, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/date-time])."},
  'invalid_value': { code: 'invalid_value', message: "[BROWSERSTACK_INVALID_VALUE_PASSED] Invalid value passed in customTime command. Please pass a valid value and try again. For more details, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/date-time])."},
  'invalid_custom_date_value': { code: 'invalid_value', message: "[BROWSERSTACK_INVALID_VALUE_PASSED] Invalid value passed in customDate command. Please pass a valid value and try again. For more details, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/date-time])."},
  'executor_internal_error': { code: 'executor_internal_error', message: "[BROWSERSTACK_UPDATE_IOS_SETTINGS_EXECUTOR_ERROR] We could not execute the customTime command successfully due to an internal issue/error. For more details, please reach out to support."},
  'custom_date_executor_internal_error': { code: 'executor_internal_error', message: "[BROWSERSTACK_UPDATE_IOS_SETTINGS_EXECUTOR_ERROR] We could not execute the customDate command successfully due to an internal issue/error. For more details, please reach out to support."},
  'feature_not_available_in_current_plan_for_aa' : { code: 'feature_not_available_in_current_plan_for_aa', message: "[BROWSERSTACK_FEATURE_NOT_AVAILABLE_IN_CURRENT_PLAN] Setting device date & time functionality is only available with App Automate Device Cloud Pro plan. Subscribe to this plan by visiting our pricing page([https://www.browserstack.com/accounts/subscriptions?product=app-automate])."},
  'invalid_date_value': { code: 'invalid_date_value', message: '[BROWSERSTACK_DATE_OUT_OF_BOUND] You can change date only upto 7 days in the future. Please pass a valid date value and try again. For more details, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/date-time]).' },
  'invalid_action_by_group': { code: 'invalid_action_by_group', message: "[BROWSERSTACK_INVALID_ACTION_BY_GROUP] Currently, this feature is not available for your group. If you have any concerns please reach out to support."},
  'incompatible_product': { code: 'incompatible_product', message: '[BROWSERSTACK_UNSUPPORTED_PRODUCT] We do not support customDate in Automate currently. For details on supported actions, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/date-time]).' }
};

exports.HOUR_FORMAT_CUSTOM_EXECUTOR_ERRORS = {
  'incompatible_os_version':  { code: 'incompatible_os_version', message: "[BROWSERSTACK_INCOMPATIBLE_OS_VERSION] Currently we support 12HourTime under updateIosDeviceSettings command only on iOS 15 & above devices. For more details, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/date-time])."},
  'invalid_value': { code: 'invalid_value', message: "[BROWSERSTACK_INVALID_VALUE_PASSED] Invalid value passed as input to 12HourTime argument in updateIosDeviceSettings command. Please pass a valid value and try again. For more details, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/date-time])."},
  'executor_internal_error': { code: 'executor_internal_error', message: "[BROWSERSTACK_UPDATE_IOS_SETTINGS_EXECUTOR_ERROR] We could not execute the 12HourFormat command successfully due to an internal issue/error. For more details, please reach out to support."},
  'feature_not_available_in_current_plan_for_aa' : { code: 'feature_not_available_in_current_plan_for_aa', message: "[BROWSERSTACK_FEATURE_NOT_AVAILABLE_IN_CURRENT_PLAN] Setting Hour Format functionality is only available with App Automate Device Cloud Pro plan. Subscribe to this plan by visiting our pricing page([https://www.browserstack.com/accounts/subscriptions?product=app-automate])."},
};

exports.LOCATION_SERVICES_CUSTOM_EXECUTOR_ERRORS = {
  'incompatible_os_version':  { code: 'incompatible_os_version', message: "[BROWSERSTACK_INCOMPATIBLE_OS_VERSION] Currently we support LocationServices command only on iOS 15 & above devices. For more details, please refer to our documentation()."},
  'invalid_value': { code: 'invalid_value', message: "[BROWSERSTACK_INVALID_VALUE_PASSED] Invalid value passed as input to LocationServices argument in updateIosDeviceSettings command. Please pass a valid value and try again. For more details, please refer to our documentation."},
  'executor_internal_error': { code: 'executor_internal_error', message: "[BROWSERSTACK_UPDATE_IOS_SETTINGS_EXECUTOR_ERROR] We could not execute the updateIosDeviceSettings command successfully due to an internal error. Please try again. For more details, please reach out to support."},
  'feature_not_available_in_current_plan_for_aa' : { code: 'feature_not_available_in_current_plan_for_aa', message: "[BROWSERSTACK_FEATURE_NOT_AVAILABLE_IN_CURRENT_PLAN] Setting ios device location services functionality is only available with App Automate Device Cloud Pro plan. Subscribe to this plan by visiting our pricing page([https://www.browserstack.com/accounts/subscriptions?product=app-automate])."},
};

exports.BIOMETRIC_USER_OPTION_CUSTOM_EXECUTOR_ERRORS = {
  'invalid_action_name': { code: 'invalid_action_name', message: "[BROWSERSTACK_INVALID_ACTION_NAME] 'biometric' action is not supported on Automate. For details on supported actions, refer to our documentation."},
  'incompatible_os_version':  { code: 'incompatible_os_version', message: "[BROWSERSTACK_INCOMPATIBLE_OS_VERSION] Currently we support biometricUserOption executor command only on iOS devices. For more details, please refer to our documentation."},
  'invalid_arg_passed': { code: 'invalid_argument_passed', message: "[BROWSERSTACK_INVALID_ARGUMENT_PASSED] Invalid argument passed as input to biometricUserOption executor command. Please pass a valid value and try again. For details on allowed values, please refer to our documentation."},
  'invalid_action_used': { code: 'invalid_action_used', message: "[BROWSERSTACK_INVALID_ACTION_USED] biometricUserOption executor command is not supported without passing the enableBiometric (or browserstack.enableBiometric) capability. For details on supported actions, refer to our documentation."},
  'invalid_value': { code: 'invalid_value', message: "[BROWSERSTACK_INVALID_VALUE_PASSED] Invalid value passed as input to userOption argument inside biometricUserOption executor command. Please pass a valid value and try again. For details on allowed values, please refer to our documentation."},
  'executor_internal_error': { code: 'executor_internal_error', message: "[BIOMETRIC_USER_OPTION_CUSTOM_EXECUTOR_ERRORS] We could not execute the biometricUserOption executor command successfully due to an internal error. Please try again. If the issue persists, please reach out to support."}
};

exports.IOS_SETTINGS_EXECUTOR_ERRORS = {
  'incompatible_product': { code: 'incompatible_product', message: "[BROWSERSTACK_UNSUPPORTED_PRODUCT] We do not support updateIosDeviceSettings command in Automate currently. For details on supported actions, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/date-time])."},
  'incompatible_platform': { code: 'incompatible_platform', message: "[BROWSERSTACK_INCOMPATIBLE_COMMAND_PASSED] updateIosDeviceSettings command is not supported on Android devices. For more details, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/date-time])."},
  'parallel_update_ios_settings_command_error': { code: 'parallel_update_ios_settings_command_error', message: '[BROWSERSTACK_MULTIPLE_IOS_SETTINGS_COMMANDS_ERROR] Running multiple updateIosDeviceSettings commands in parallel is currently not supported on BrowserStack devices. For more details, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/date-time]).' },
  'invalid_argument': { code: 'invalid_argument', message: '[BROWSERSTACK_INVALID_ARGUMENT_PASSED] Invalid argument passed in updateIosDeviceSettings executor command. Please pass a valid argument and try again. For more details, please refer to our documentation([https://www.browserstack.com/docs/app-automate/appium/advanced-features/date-time]).' }

};

exports.SETTINGS_APP_CUSTOM_EXECUTOR = {
  max_count : 50,
  max_keys: 100,
  timeout: 260, // this is kept greater than 240, current so-timeout values
};

exports.PERCY_SCREENSHOT_CUSTOM_EXECUTOR_ERRORS = {
  'invalid_arg_passed': { code: 'invalid_argument_passed', message: "[BROWSERSTACK_INVALID_ARGUMENTS_PASSED] Arguments passed are invalid. For details on supported arguments, refer to our documentation or reach out to support."},
  'invalid_percy_build_info': { code: 'invalid_percy_build_info', message: "[BROWSERSTACK_COMMAND_EXECUTION_FAILED] Current Automation build is already marked with a different percy build. For details on how to use this, refer to our documentation or reach out to support."},
  'unknown': { code: 'unknown', message: "[BROWSERSTACK_COMMAND_EXECUTION_FAILED] Something went wrong while executing the command. Please retry and if the error persists, refer to our documentation or reach out to support."},
  'invalid_combination_passed': { code: 'invalid_combination_passed', message: "[PERCY_UNSUPPORTED_COMBINATION_PASSED] OS/Browser/Selenium combination is not supported, refer to our documentation or reach out to support."},
};

exports.PERCY_SCREENSHOT_UPLOADER_CERTS = JSON.parse(Buffer.from(configFile['percy_screenshot_uploader_cert'], 'base64').toString('binary'));

const imageInjectionSupportedTypes = ['.jpg', '.jpeg', '.png'];
const videoInjectionSupportedTypes = ['.mp4'];

exports.VIDEO_INJECTION_SUPPORTED_VIDEOS = videoInjectionSupportedTypes
  .concat(videoInjectionSupportedTypes.map(name => name.toUpperCase()));

exports.IMAGE_INJECTION_SUPPORTED_IMAGES = imageInjectionSupportedTypes
  .concat(imageInjectionSupportedTypes.map(name => name.toUpperCase()));

exports.AUDIO_INJECTION_SUPPORTED_FORMATS = {
  'app_automate': {
    'android': ['.mp3', '.wav', '.aac', '.ogg', '.wma']
  },
  'automate': {
    'android': ['.mp3', '.wav']
  }
};

exports.ENABLE_EXPERIMENT_SKIP_JAR_DELETE = configFile.enable_experiment_skip_jar_delete || false;

exports.CDP_SELENIUM_VERSIONS_EXTRA = ['4.0.0-rc-1'];
exports.CDP_SELENIUM_VERSIONS = '4.0.0';
exports.CDP_SELENIUM_VERSIONS_IGNORE = [];
exports.BIDI_SELENIUM_VERSIONS_EXTRA = [];
exports.BIDI_SELENIUM_VERSIONS = '4.20.0';
exports.BIDI_SELENIUM_VERSIONS_IGNORE = [];
exports.DELETE_BROWSER_VERSION_FOR_SELENIUM_VERSIONS_EXTRA = ['4.0.0-beta-1', '4.0.0-beta-2', '4.0.0-beta-3', '4.0.0-rc-1'];
exports.DELETE_BROWSER_VERSION_FOR_SELENIUM_VERSIONS = '4.0.0';
exports.DELETE_BROWSER_VERSION_FOR_SELENIUM_VERSIONS_IGNORE = [];
exports.DELETE_CHROME_OPTIONS_FOR_EDGE_CHROMIUM_FOR_SELENIUM_VERSIONS_EXTRA = ['4.0.0-beta-2', '4.0.0-beta-3', '4.0.0-rc-1'];
exports.DELETE_CHROME_OPTIONS_FOR_EDGE_CHROMIUM_FOR_SELENIUM_VERSIONS = '4.0.0';
exports.DELETE_CHROME_OPTIONS_FOR_EDGE_CHROMIUM_FOR_SELENIUM_VERSIONS_IGNORE = [];

exports.PUPPETEER_CONSTANTS = {
  PUPPETEER: "puppeteer",
  ERROR_MESSAGE: {
    TERMINAL_ALLOCATION_FAILURE: "No response from BrowserStack"
  }
};

exports.CONSOLE_LOG_LEVELS = new Map([
  ['disable', -1],
  ['error', 1],
  ['errors', 1],
  ['warning', 2],
  ['warnings', 2],
  ['info', 3],
  ['verbose', 4]
]);

exports.ANNOTATE_LOG_LEVELS = new Map([
  ["info", 0],
  ["debug", 1],
  ["warn", 2],
  ["error", 3]
]);

exports.VERBOSE_LOG_LEVEL = 4;

exports.COMMAND_ERROR_MESSAGES = {
  'multipleApksNotSupported': "Appium's installMultipleApks command is currently not supported on BrowserStack devices, you can use midSessionInstallApps capability to install apps in between your Appium test session. For more details, please refer to our documentation: https://www.browserstack.com/docs/app-automate/appium/advanced-features/test-app-upgrades'"
};

exports.log_meta = {
  hostname: hostname,
  component: hostname.split('-')[0],
  application: 'hubnodes',
  team: 'automate'
};

exports.FUNCTIONAL_TESTING_DOMAIN = configFile['functional_testing_domain'] || 'hub-ft.browserstack.com';
exports.FUNCTIONAL_TESTING_PACKAGE_NAME = 'functional_testing';
exports.FT_PRODUCT_NAME_BRIEF = 'ft';
exports.CBT_PRODUCT_NAME_BRIEF = 'cbt';

exports.JSON_PACKET_THRESHOLD = 7500;
exports.SET_DATA_THRESHOLD = {key: 45, value: 8000};

exports.SAMSUNG_MOBILE_CONSTANTS = {
  "platformName": "Android",
  "appPackage": "com.sec.android.app.sbrowser",
  "appActivity": ".SBrowserMainActivity",
  "androidDeviceSocket": "Terrace_devtools_remote",
  "androidExecName": "Terrace"
};

exports.ADB_COMMAND_EXECUTOR = {
  'invalid_action_name': { code: 'invalid_action_name', message: "[BROWSERSTACK_INVALID_ACTION_NAME] 'adbShell' action is not supported. For details on supported actions, refer to our documentation."},
  'invalid_os': {code: 'invalid_os', message: "[BROWSERSTACK_INCOMPATIBLE_OS] adbShell custom executor is only supported for Android devices."},
  'unsupported_device': {code: 'unsupported_device', message: "[BROWSERSTACK_UNSUPPORTED_DEVICE]  <COMMAND> is not supported on <DEVICE_NAME> devices. Please refer to our documentation for the list of supported and unsupported devices for this feature."},
  'incompatible_os_version': {code: 'incompatible_os_version', message: "[BROWSERSTACK_INCOMPATIBLE_OS_VERSION]  <COMMAND> is not supported on <ANDROID_VERSIONS> devices. Please refer to our documentation for the right command for this OS version."},
  'blacklisted_command': {code: 'blacklisted_command', message: "BROWSERSTACK_UNSUPPORTED_ADB_SHELL_COMMAND]  <COMMAND> is not supported on BrowserStack currently. For more details on what commands we support, please refer to our documentation."},
  'internal_error': {code: 'internal_error', message: "[BROWSERSTACK_INTERNAL_ERROR] Could not execute the command due to an internal error. Please retry and if the error persists, reach out to support. "},
  'invalid_format': { code: 'invalid_format', message: "[BROWSERSTACK_INVALID_FORMAT]  Invalid format provided for the BrowserStack custom executor. For details on the right format, please refer our documentation. If the error persists, please reach-out to support."},
  'parallel_adb_command_error': { code: 'parallel_adb_command_error', message: "[BROWSERSTACK_MULTIPLE_ADB_COMMANDS_ERROR] Running multiple ADB commands in parallel is currently not supported on BrowserStack Android devices. For more details, please check our documentation or reach out to support."},
  'timedout': { code: 'timedout', message: "[BROWSERSTACK_COMMAND_TIMEOUT] Command timed-out as we could not get any response in 10 seconds. Please retry and if the error persists, reach out to support."},
  'max_adb_command_error': { code: 'max_adb_command_error', message: "[BROWSERSTACK_ADB_COMMANDS_LIMIT_REACHED_ERROR] You can execute up to 10000 ADB commands per session on BrowserStack Android devices. For more details, please check our documentation or reach out to support."},
  'device_orientation_not_in_aa_plan_error': {code : 'device_orientation_not_in_aa_plan_error', message: "[BROWSERSTACK_FEATURE_NOT_AVAILABLE_IN_CURRENT_PLAN] Setting device orientation (sensor based apps) is only available with App Automate Device Cloud Pro plan. Subscribe to this plan by visiting our pricing page([https://www.browserstack.com/accounts/subscriptions?product=app-automate])."}
};

exports.ADB_COMMANDS_LIMIT_PER_SESSION = 10000;

exports.BRIDGECLOUD_PARAMS = [
  'dedicated_cleanup',
  'dedicated_cleanup_config',
  'is_dedicated_cloud_session',
  'app_testing_app_version',
  'dedicated_minified_cleanup',
  'is_an_instant_app',
  'instant_app_enabled_group'
];

exports.DELETE_ALL_COOKIES_PAYLOAD = "document.cookie.split(\";\").forEach(function(c) { document.cookie = c.replace(/^ +/, \"\").replace(/=.*/, \"=;expires=\" + new Date().toUTCString() + \";path=/\"); });";
exports.DELETE_NAMED_COOKIES_PAYLOAD = "document.cookie = arguments[0] + '=;expires=Thu, 01 Jan 1970 00:00:01 GMT;'";
exports.CLICK_SCRIPT_PAYLOAD = "var ele=arguments[0],tagName=ele.tagName?ele.tagName.toLowerCase():undefined,type=ele.type?ele.type.toLowerCase():undefined;const inputClickType=['submit','button','color','file','image','reset'],inputCheckedType=['radio','checkbox'];switch(tagName){case'input':inputCheckedType.includes(type)?ele.checked=!ele.checked:inputClickType.includes(type)?ele.click():ele.focus();break;case'option':ele.selected=!ele.selected;break;default:ele.click()}";
exports.ELEMENT_KEY = 'element-6066-11e4-a52e-4f735466cecf'; // https://www.w3.org/TR/2015/WD-webdriver-20150827

exports.BECOME_MASTER_CAPABILITY_CHECK_PERIOD_MS = 5 * 60 * 1000;
exports.UPLOADER_DISK_USAGE_THRESHOLD = 85;
exports.DISK_CHECK_TIMEOUT_MS = 5000;

exports.APPLE_OS = ["ios", "tvos"];
exports.PRIVATE_DOMAIN_OR_IP_REGEX = [
  /localhost/,
  /bs-local.com/,
  /^127\./,
  /^10\./,
  /^172\.1[6-9]\./,
  /^172\.2[0-9]\./,
  /^172\.3[0-1]\./,
  /^192\.168\./
];

exports.MOCKING_PEFROMANCE_ENDPOINTS = {
  "POST" : {
    "timeouts": {
      "data": '{"value": null}'
    },
    "url": {
      "data": '{"value": null}'
    },
    "back": {
      "data": '{"value": null}'
    },
    "refresh": {
      "data": '{"value": null}'
    },
    "forward": {
      "data": '{"value": null}'
    },
    "value": {
      "data": '{"value": null}'
    },
    "clear": {
      "data": '{"value": null}'
    },
    "accept": {
      "data": '{"value": null}'
    },
    "dismiss": {
      "data": '{"value": null}'
    },
    "actions": {
      "data": '{"value": null}'
    },
    "cookie": {
      "data": '{"value": null}'
    },
    "frame": {
      "data": '{"value": null}'
    },
    "click": {
      "data": '{"value": null}'
    },
    "window": {
      "data": '{"value": null}'
    },
    "element": {
      "data": '{"value":{"element-6066-11e4-a52e-4f735466cecf":"afaa7ee2-1668-453c-8ea5-ff75b9920801"}}'
    },
    "elements": {
      "data": '{"value":[{"element-6066-11e4-a52e-4f735466cecf":"2c3e6d7f-eeeb-4518-a0e0-f327ac463ef6"},{"element-6066-11e4-a52e-4f735466cecf":"d01083f7-76c0-4906-af6b-9eea4463e4f4"},{"element-6066-11e4-a52e-4f735466cecf":"ef1fd942-e445-4651-b8ba-e4406b9c40e9"},{"element-6066-11e4-a52e-4f735466cecf":"4123e20b-a0bb-423e-aa9b-6335fe6abe42"},{"element-6066-11e4-a52e-4f735466cecf":"71631fc7-254a-4e63-ab64-029cb4eefa79"}]}'
    },
    "sync": {
      "data": '{"value": "Testing performance"}'
    },
    "log": {
      "data": '{"value": "Testing performance"}'
    },
  },
  "DELETE" : {
    "cookie": {
      "data": '{"value": null}'
    },
  },
  "GET" : {
    "url" : {
      "data" : '{"value": "Testing performance"}'
    },
    "text" : {
      "data": '{"value": "Testing performance"}'
    },
    "title" : {
      "data": '{"value": "Testing performance"}'
    },
    "name" : {
      "data": '{"value": "Testing performance"}'
    },
    "screenshot" : {
      "data": '{"value": "Testing performance"}'
    },
    "font-size" : {
      "data": '{"value": "Testing performance"}'
    },
    "selected" : {
      "data": '{"value": true}'
    },
    "enabled" : {
      "data": '{"value": true}'
    },
    "window" : {
      "data": '{"value": "********************************"}'
    },
    "handles" : {
      "data": '{"value": ["********************************"]}'
    },
    "rect" : {
      "data": '{"value": {"height":1013,"width":1200,"x":22,"y":45}}'
    },
    "active" : {
      "data": '{"value": {"element-6066-11e4-a52e-4f735466cecf":"dd1a0e3f-fee4-49e0-95d3-c23c9a97d77b"}}'
    },
    "cookie" : {
      "data": '{"value": []}'
    }
  }
};

exports.LIGHTHOUSE_AUTOMATE = {
  timeout: 3 * 60 * 1000,
  buffer_timeout: 10 * 1000, // A buffer to account for proper handling of timeout on terminal side
  endpoint: "/lighthouse_automate",
  jsonListEndpoint: "/json/list",
};

const browserVersionStrToFloat = (version) => {
  return isNotUndefined(version) && typeof version === "string"
    ? parseFloat(version.split(" ")[0])
    : 0;
};
exports.browserVersionStrToFloat = browserVersionStrToFloat;

exports.LH_SUPPORTED_BROWSERS = ["chrome", "chromium", "edge"];
exports.LH_MIN_CHROME = 90;

exports.LH_ERROR_MESSAGES = {
  framework_not_supported: "Lighthouse support is only for Playwright sessions",
  browser_not_supported:
    "Lighthouse runs are supported only on Google Chrome and MS Edge browsers",
  lh_error: "Error in lighthouse run",
  chrome_version_not_supported: `Minimum supported Google Chrome and MS Edge version for lighthouse is ${this.LH_MIN_CHROME}`,
  js_executor_url_not_found: "Error in getting current url for lighthouse js executor",
  js_executor_assert_limit_not_found: "Either \"performance:assert\" capability was not passed or running on unsupported browser combination. Please refer to the documentation.",
  js_executor_framework_not_supported: "Lighthouse executor is only supported only on Selenium / Playwright / Puppeteer sessions.",
  js_executor_invalid_lh_config: "Invalid lhConfig passed. Please refer to the documentation.",
  js_executor_invalid_throttling: "Invalid throttling value passed. Allowed values: mobileSlow4G, mobileRegular3G, desktopDense4G. Please refer to the documentation.",
  js_executor_invalid_categories_format: "Invalid categories format used, please use JSON format.",
  js_executor_invalid_categories: "Invalid categories value passed in assertResult. Please check documentation for supported categories.",
  js_executor_invalid_categories_value: "Invalid categories value in assertResult. Please provide value between 0 and 100.",
  js_executor_invalid_metrics_format: "Invalid metrics threshold passed in assertResult, it should contain moreThan / lessThan and the metricUnit. Please refer to the documentation.",
  js_executor_invalid_metrics_value: "Invalid metrics in assertResult. Please provide a numerical value.",
  undefined_test_url: "URL not provided or undefined",
  report_limit_exhausted: "Exceeded the report limit, current limit is "
};

exports.LH_PERFORMANCE_ERROR_MESSAGES = {
  JS_EXECUTOR_URL_NOT_FOUND: "js_executor_url_not_found",
  ERROR_IN_RUN_LIGHTHOUSE: "error_in_run_lighthouse", // Error occurred before Lighthouse execution, such as exceeding the report generation limit or empty url.
  LIGHTHOUSE_ERROR: "lighthouse_error", // Error during Lighthouse execution or errors returned in the Lighthouse response.
  ASSERTION_ERROR: "assertion_error"
};

exports.LH_FAIL_SESSION_ARG = {
  status: "failed",
  reason: "Lighthouse assertion failed",
};

exports.LH_DEFAULT_CONFIG = {
  extends: "lighthouse:default"
};

/* From https://github.com/GoogleChrome/lighthouse/blob/main/core/config/constants.js */
const DEVTOOLS_RTT_ADJUSTMENT_FACTOR = 3.75;
const DEVTOOLS_THROUGHPUT_ADJUSTMENT_FACTOR = 0.9;
exports.THROTTLING_PROFILE = {
  DEVTOOLS_RTT_ADJUSTMENT_FACTOR,
  DEVTOOLS_THROUGHPUT_ADJUSTMENT_FACTOR,
  mobileSlow4G: {
    rttMs: 150,
    throughputKbps: 1.6 * 1024,
    requestLatencyMs: 150 * DEVTOOLS_RTT_ADJUSTMENT_FACTOR,
    downloadThroughputKbps: 1.6 * 1024 * DEVTOOLS_THROUGHPUT_ADJUSTMENT_FACTOR,
    uploadThroughputKbps: 750 * DEVTOOLS_THROUGHPUT_ADJUSTMENT_FACTOR,
    cpuSlowdownMultiplier: 4,
  },
  mobileRegular3G: {
    rttMs: 300,
    throughputKbps: 700,
    requestLatencyMs: 300 * DEVTOOLS_RTT_ADJUSTMENT_FACTOR,
    downloadThroughputKbps: 700 * DEVTOOLS_THROUGHPUT_ADJUSTMENT_FACTOR,
    uploadThroughputKbps: 700 * DEVTOOLS_THROUGHPUT_ADJUSTMENT_FACTOR,
    cpuSlowdownMultiplier: 4,
  },
  desktopDense4G: {
    rttMs: 40,
    throughputKbps: 10 * 1024,
    cpuSlowdownMultiplier: 1,
    requestLatencyMs: 0, // 0 means unset
    downloadThroughputKbps: 0,
    uploadThroughputKbps: 0,
  },
};
/* ------------------- */

exports.DF_FEATURE_FENCE_CODES = {
  DEVICE_ORIENTATION_CODE: "aa-app-orientation",
  CUSTOM_TIME_PLAN_CODE: "aa-device-time",
  CUSTOM_DATE_PLAN_CODE: "aa-device-date",
  CUSTOM_LOCATION_SERVICES_PLAN_CODE: "aa-device-location",
  DEVICE_SHAKE_CODE: "aa-gestures-device-shake",
  APP_SETTINGS: "aa-ios-app-settings"
};

exports.NUDGE_LOCAL_ERRORS = [
  'ERR_FAILED',
  'ERR_TIMED_OUT',
  'ERR_BLOCKED_BY_CLIENT',
  'ERR_NETWORK_CHANGED',
  'ERR_SOCKET_NOT_CONNECTED',
  'ERR_CONNECTION_CLOSED',
  'ERR_CONNECTION_RESET',
  'ERR_CONNECTION_REFUSED',
  'ERR_CONNECTION_ABORTED',
  'ERR_CONNECTION_FAILED',
  'ERR_NAME_NOT_RESOLVED',
  'ERR_ADDRESS_INVALID',
  'ERR_ADDRESS_UNREACHABLE',
  'ERR_TUNNEL_CONNECTION_FAILED',
  'ERR_CONNECTION_TIMED_OUT',
  'ERR_SOCKS_CONNECTION_FAILED',
  'ERR_SOCKS_CONNECTION_HOST_UNREACHABLE',
  'ERR_PROXY_CONNECTION_FAILED',
  'ERR_NAME_NOT_RESOLVED',
  'ERR_NAME_RESOLUTION_FAILED',
  'ERR_MANDATORY_PROXY_CONFIGURATION_FAILED'
];

exports.POST_FIRE_CMD_FRAMEWORKS = [
  'puppeteer'
];

// maps error codes sent by realmobile to error message to be shown
exports.DF_EXECUTOR_ERROR_CODES = {
  'CD_0001': this.DATE_TIME_CUSTOM_EXECUTOR_ERRORS['invalid_date_value']
};


exports.CACHE_ERROR_LIST = ['invalid_desktop_caps', 'automate_access_prohibited', 'invalid_input_caps', 'deprecated_os_version', 'invalid_os_version', 'invalid_mobile_caps', 'invalid_params', 'always_enable_local_testing', 'app_automate_access_prohibited', 'browserstack_invalid_action_by_group', 'invalid_device_name', 'non_whitelisted_group', 'browserstack_incompatible_os_version', 'browserstack_incompatible_capabilities_passed', 'incompatible_os_version', 'missing_enable_sim_caps'];
exports.AA_CACHE_ERROR_LIST = ['invalid_input_caps', 'deprecated_os_version', 'invalid_os_version', 'invalid_mobile_caps', 'invalid_params', 'always_enable_local_testing', 'app_automate_access_prohibited', 'browserstack_invalid_action_by_group', 'invalid_device_name', 'non_whitelisted_group', 'browserstack_incompatible_os_version', 'browserstack_incompatible_capabilities_passed', 'incompatible_os_version', 'missing_enable_sim_caps', 'app_automate.browserstack.timezone_feature_inaccessible', 'esim_incompatible_os', 'apple_pay_resign_app', 'browserstack_incompatible_ios_version', 'device_unsupported_for_georestricted_region', 'device_unavailable_in_freemium', 'dda_validations_error', 'app_automate.timezone_feature_inaccessible', 'local_connection_error', 'app-unreserved-pvt-terminal-requested', 'compatible_os_version_missing_on_browserstack'];

// Search in O(1)
exports.CACHE_ERRORS_CODE_STR = {};
exports.CACHE_ERROR_LIST.forEach((key) => {
  exports.CACHE_ERRORS_CODE_STR[key] = true;
});

// For AA
exports.AA_CACHE_ERRORS_CODE_STR = {};
exports.AA_CACHE_ERROR_LIST.forEach((key) => {
  exports.AA_CACHE_ERRORS_CODE_STR[key] = true;
});

// 0 -> disabled, 1 -> enabled
exports.ENABLE_GENERIC_CACHING = 0;

exports.SAMSUNG_SET_CONTEXT_DELAY = 1000;

exports.IDEMPOTENCY_KEY_HEADER = ['x-idempotency-key', 'X-Idempotency-Key'];

exports.GEOCOMPLY_PROXYBYPASS_URLS = "wss.plc-gc.com";

const chrome_ai_extension_path = path.join(__dirname, 'ai_extensions/chrome_extension.crx').toString('base64');
const firefox_ai_extension_path = path.join(__dirname, 'ai_extensions/firefox_extension.xpi').toString('base64');

exports.AI_EXTENSIONS = {
  'chrome': fs.readFileSync(chrome_ai_extension_path, 'base64'),
  'firefox': fs.readFileSync(firefox_ai_extension_path, 'base64')
};

exports.TCG_SERVICE = {
  'scheme': configFile['tcg_service']['scheme'],
  'regions': configFile['tcg_service']['tcg_regions'],
  'username': configFile['tcg_service']['username'],
  'password': configFile['tcg_service']['password'],
  'timeout': 10000,
};

exports.AI_FIND_ELEMENT_FAILURE_SCRIPT_TIMEOUT = configFile['ai_failure_event_script_timeout'];
exports.AI_FIND_ELEMENT_FAILURE_PAGE_LOAD_TIMEOUT = configFile['ai_failure_event_page_load_timeout'];

exports.LCNC_GROUPS = configFile['lcnc_groups'];


exports.USE_SELENIUM_EXECUTE_ASYNC_IN_AI = false;
exports.DO_CALL_TO_AI_EXTENSION_IN_ASYNC = false;


exports.QUEUE_SIZE_EXCEEDED = {
  errorMessage: "[BROWSERSTACK_QUEUE_SIZE_EXCEEDED] All parallel tests are currently in use, and the number of sessions have exceeded the queue limit. Please wait to finish or upgrade your plan to add more sessions",
  reason: "queue_size_exceeded"
};

exports.NODE_RETRY_ERROR_CODES = ['ECONNRESET', 'ETIMEDOUT', 'EHOSTUNREACH', 'ECONNREFUSED', 'ENETUNREACH'];
exports.NODE_RETRY_SAME_REGION_ERROR_CODES = [];


exports.ENABLE_SAFARI_START_FLOW = false;

exports.INSTRUMENT_EXECUTE_CLICK = false;

exports.A11Y_PRESIGNED_URL_KEY = "A11Y-PRESIGNED-URL";

exports.SEND_RAW_LOGS_BOTH = true;


// private region name of hub
exports.PRIVATE_HUB_REGIONS = new Set(['us-east-3b', 'eu-west-1', 'ap-southeast-2']);
// key is the region name of hub, value is the sub region name in terminal
exports.PRIVATE_HUB_REGIONS_TERMINAL_MAPPING = {
  'us-east-3b': 'us-east-3b',
  'eu-west-1': 'eu-west-1a',
  'ap-southeast-2': 'ap-southeast-2a'
};


exports.APP_ALLY_CUSTOM_EXECUTOR_ERRORS = {
  'invalid_arg_passed': { code: 'invalid_argument_passed', message: "[BROWSERSTACK_INVALID_ARGUMENTS_PASSED] Arguments passed are invalid. For details on supported arguments, refer to our documentation or reach out to support."},
  'invalid_combination_passed': { code: 'invalid_combination_passed', message: "[APP_ACCESSIBILITY_UNSUPPORTED_COMBINATION_PASSED] OS/Browser/Device combination is not supported, refer to our documentation or reach out to support."},
};

// Device Log Error Constants
exports.DEVICE_LOG_ERRORS = {
  'device_logs_not_enabled': { code: 'device_logs_not_enabled', message: 'Device logs must be enabled for this session' },
  'internal_error': { code: 'internal_error', message: '[BROWSERSTACK_INTERNAL_ERROR] We couldn\'t retrieve device logs. Please try again.' }
};

exports.APP_ALLY_AWS_KEYS = configFile["app_ally_aws_key"];
exports.APP_ALLY_AWS_SECRET = configFile["app_ally_aws_secret"];
exports.APP_ALLY_AWS_BUCKET = configFile["app_ally_aws_bucket"];
exports.APP_ALLY_AWS_REGION = configFile["app_ally_aws_region"];

exports.PW_FIND_ELEMENT_ALLOWED_METHODS = [
  "check", "click", "dblclick", "setChecked", "tap", "uncheck",
  "hover", "dragTo", "screenshot", "fill", "clear",
  "selectOption", "selectText", "focus", "press", "pressSequentially"
];
