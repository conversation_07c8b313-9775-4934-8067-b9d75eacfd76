#!/usr/bin/node

'use strict';

const constants = require('./constants');
// Add dnsCache at the beginning to cache the dns for
// all the internal clients as well
const dnsCache = require('./helpers/dnscache');
const { Chitragupta } = require('chitragupta');

dnsCache({
  'enable': true,
  'ttl': 30,
  'cachesize': 1000,
});

const browserstack = require('./browserstack');
const urlModule = require('url');
const HubLogger = require('./log');
const ha = require('./ha');
const http = require('http');
const util = require('util');
const helper = require('./helper');
const pubSub = require('./pubSub');
const chunkedResponse = require('./chunkedResponse');
const nodeurl = require('url');
const bridge = require('./bridge');
const requestlib = require('./lib/request');
const supporting = require('./supporting');
const startSession = require('./start_session');
const Promise = require('bluebird');
const queueHandler = require('./queueHandler');
const statProcessor = require('./process_stats');
const eventLoopLogger = require('blocked');
// Using blocked-at behind feature flag for stacktrace
const blockedAt =  require('blocked-at');
const { detectClientDialect, mapRequestToAppropriateDialect, mapResponseToAppropriateDialect } = require('./utils/commandMapper');
const path = require('path');
const iOSAcceptSSLCertHandler = require('./helpers/customSeleniumHandling/acceptSSLHandler/iOSAcceptSSLCertHandler').sslHandler;
const tracker = require('./utils/performance-tracker');
const isReqFromBrowserstack = require('./utils/verifyRequestHeaders');
const redactor = require('./helpers/redactor');
const w3cHelper = require('./helpers/w3CHelper');
const capabilitiesRedactor = require('./helpers/redactor/capabilities');
const { isString, isTrueString, isNotUndefined, isUndefined } = require('./typeSanity');
const { Events } = require('browserstack-dwh');
const { clearSemaphore } = require('./semaphore');
const browserstackErrorUtil = require('./helpers/browserstackErrorBucketUtil');
const WebSocketHandler = require('./webSocketHandler');
const { REMOTE_DEBUGGER_PORT } = require('./config/socketConstants');
const { getCompleteFileName } = require('./helpers/debugScreenshots');
const loggerjs = require('./logger');
const { isCDP } = require('./socketManagers/validations');
const S3UploadHelper = require('./helpers/s3UploadHelper');
const errorMessages = require('./errorMessages');
const { checkResponseOnline } = require('./clientRequest/responseChecks');
const healthCheck = require('./healthCheck');
const { mapSafariDriverRequest } = require('./lib/commandMapper/safariDriverIos');
const { router } = require('./routes');
const { updateKeyObjectForUdpKeys } = require('./controllers/seleniumCommand/helpers/baseHandlerHelper');
const { sendCombinedLogs, LOG_TYPE } = require('./utils/logger/loggingHelper');
const sessionManagerHelper = require('./services/session/sessionManagerHelper');
const { IESendKeysHandler } = require('./controllers/seleniumCommand/handlers/IESendKeysHandler');
const { UrlHttpsAuthHandler } = require('./controllers/seleniumCommand/handlers/UrlHttpsAuthHandler');
const { BasicAuthEdgeHandler } = require('./controllers/seleniumCommand/handlers/BasicAuthEdgeHandler');
const { AndroidMultiApksHandler } = require('./controllers/seleniumCommand/handlers/AndroidMultiApksHandler');
const { StopSessionHandler } = require('./controllers/seleniumCommand/handlers/StopSessionHandler');
const { FirefoxAcceptSslHandler } = require('./controllers/seleniumCommand/handlers/FirefoxAcceptSslHandler');
const { JSSendKeysHandler } = require('./controllers/seleniumCommand/handlers/JSSendKeysHandler');
const { AndroidOrientationHandler } = require('./controllers/seleniumCommand/handlers/AndroidOrientationHandler');
const { AppiumEmulatorOrientationHandler } = require('./controllers/seleniumCommand/handlers/AppiumEmulatorOrientationHandler');
const { FirefoxConsoleLogs } = require('./controllers/seleniumCommand/handlers/FirefoxConsoleLogsHandler');
const { ExecuteScriptHandler } = require('./controllers/seleniumCommand/handlers/ExecuteScriptHandler');
const { IOSFileUploadHandler } = require('./controllers/seleniumCommand/handlers/IOSFileUploadHandler');
const { IOSGetLocationHandler } = require('./controllers/seleniumCommand/handlers/IOSGetLocationHandler');
const { ChromeMacMaximizeHandler } = require('./controllers/seleniumCommand/handlers/ChromeMacMaximizeHandler');
const { AndroidPushFileHandler } = require('./controllers/seleniumCommand/handlers/AndroidPushFileHandler');
const { EdgeProxyPollingCheck } = require('./controllers/seleniumCommand/handlers/EdgeProxyPollingCheck');
const { TestCafeDummyResponseHandler } = require('./controllers/seleniumCommand/handlers/TestCafeDummyResponseHandler');
const { LaunchAppHandler } = require('./controllers/seleniumCommand/handlers/LaunchAppHandler');
const { ResetAppHandler } = require('./controllers/seleniumCommand/handlers/ResetAppHandler');
const { IOSLocationHandler } = require('./controllers/seleniumCommand/handlers/IOSLocationHandler');
const { DeviceLogHandler } = require('./controllers/seleniumCommand/handlers/DeviceLogHandler');
const { InstallAppHandler } = require('./controllers/seleniumCommand/handlers/InstallAppHandler');
const { IOSAppStringHandler } = require('./controllers/seleniumCommand/handlers/IOSAppStringHandler');
const { IOSScreenshotHandler } = require('./controllers/seleniumCommand/handlers/IOSScreenshotHandler');
const { IEKeysHandler } = require('./controllers/seleniumCommand/handlers/IEKeysHandler');
const { IOSMaximizeHandler } = require('./controllers/seleniumCommand/handlers/IOSMaximizeHandler');
const { GetTypesHandler } = require('./controllers/seleniumCommand/handlers/GetTypesHandler');
const { W3CSendKeysHandler } = require('./controllers/seleniumCommand/handlers/W3CSendKeysHandler');
const { GetSessionHandler } = require('./controllers/seleniumCommand/handlers/GetSessionHandler');
const { DesktopOrientationHandler } = require('./controllers/seleniumCommand/handlers/DesktopOrientationHandler');
const { AICommandHandler } = require('./controllers/seleniumCommand/handlers/AICommandHandler');
const AICommandHelper = require('./controllers/seleniumCommand/helpers/AICommandHelper');
const { TCG_ENDPOINTS, TCG_HEADERS } = require('./controllers/seleniumCommand/helpers/AICommandHelper');
const kafkaProducer = require('./lib/kafka/kafkaProducer');
const WorkerPool = require('./hubWorker/workerPoolManager');
const { markAsSensitive, redactRequestEndpoints, checkRequestForRedaction } = require('./helpers/maskSensitiveInformation');

const ERROR = constants.ERROR;
const LL = constants.LOG_LEVEL;
const queued = {};


// Set for the sessionIds which got region-down as a failure, need to reset the retries for these sessionIds when they toggle
const regionDownQueued = new Set([]);

http.globalAgent.maxSockets = Infinity;
kafkaProducer.initKafkaProducers();
kafkaProducer.setKafkaWorkerPool(WorkerPool);

exports.enableLongJohn = function(){
  if (constants.isProductionEnv){
    return;
  }
  var longjohn = require('longjohn');
  longjohn.async_trace_limit = 5;
};

if(constants.USE_LONGJOHN) exports.enableLongJohn();

// Log event-loop time
eventLoopLogger(function (ms) {
  constants.pushToHootHootRegistry['maxEventLoop'] = constants.pushToHootHootRegistry['maxEventLoop'] || {};
  constants.pushToHootHootRegistry['minEventLoop'] = constants.pushToHootHootRegistry['minEventLoop'] || {};

  constants.pushToHootHootRegistry['maxEventLoop'][constants.hubName] = (constants.pushToHootHootRegistry['maxEventLoop'][constants.hubName] || 0); //Zero Because time cant be negative
  constants.pushToHootHootRegistry['maxEventLoop'][constants.hubName] = Math.max(constants.pushToHootHootRegistry['maxEventLoop'][constants.hubName], ms);

  constants.pushToHootHootRegistry['minEventLoop'][constants.hubName] = (constants.pushToHootHootRegistry['minEventLoop'][constants.hubName] || Number.MAX_SAFE_INTEGER); //Setting to max value means no eventloop call.
  constants.pushToHootHootRegistry['minEventLoop'][constants.hubName] = Math.min(constants.pushToHootHootRegistry['minEventLoop'][constants.hubName], ms);
  HubLogger.miscLogger('Event-loop', 'Blocked for: ' + ms + ' ms', LL.INFO);
}, { threshold: 50 });

let blockedAtInstance = null;
exports.usingBlockedAt = function (flag = constants.useBlockedAt){
  if (blockedAtInstance) {
    blockedAtInstance.stop();
    blockedAtInstance = null;
  }
  if(flag) {
    blockedAtInstance = blockedAt((time, stack) => {
      // console.log('Event-loop', 'Blocked for: ' + time + ' ms' + " StackTrace: " + stack);
      HubLogger.miscLogger('Event-loop', 'Blocked for: ' + time + ' ms' + " StackTrace: " + stack, LL.INFO);
    }, { threshold: 50 });
  }
};
exports.usingBlockedAt();

exports.stopBlockedAt = function() {
  if (blockedAtInstance) {
    blockedAtInstance.stop();
    blockedAtInstance = null;
  }
};

var trapGlobalException = function(err) {
  var stackTrace = err.stack ? err.stack.toString() : err.toString();
  HubLogger.exceptionLogger('EXCEPTION IN HUB PROCESS: ' + stackTrace, 'Global exception');
  if(stackTrace.indexOf('takeScreenshotAndUpload') < 0 && stackTrace.indexOf('uploadScreenshotToS3') < 0) {
    var kind = 'hub-error';
    if(stackTrace.indexOf('---------') == -1)
      kind = 'hub-error-no-trace';

    if(!constants.USE_LONGJOHN) exports.enableLongJohn();
  } else {
    kind = 'hub-snapshot-failed';
  }
  var exception_genre = 'default';
  if(err && err.toString() && err.toString().length > 0) {
    exception_genre = helper.getGlobalExceptionGenre(err.toString().toLowerCase());
  }
  var hoothoot_kind = 'global_exception';
  if( kind === 'hub-snapshot-failed' ) {
    hoothoot_kind = kind + hoothoot_kind;
  }
  HubLogger.hoothoot.emit(hoothoot_kind, 1, { genre: exception_genre, worker: (constants.worker_id || 0) });
  HubLogger.seleniumStats(kind, {}, err.toString(), stackTrace, '', exception_genre);
  if (!constants.isProductionEnv) {
    var subject = `Global Exception: ${err.toString()}`;
    var message = `Global Exception: ${err.toString} ${stackTrace} ${exception_genre}`;

    helper.sendAlerts(subject, message);
    throw new Error('Global Error: ' + JSON.stringify(err) + ' trace: ' + err.stack);
  }
};
exports.trapGlobalException = trapGlobalException;

process.on('uncaughtException', trapGlobalException);
process.on('unhandledRejection', trapGlobalException);

exports.setWorkerId = function(worker_id){
  constants.worker_id = worker_id;
};

exports.setShouldDieKeepAliveHeader = function() {
  constants.shouldDieKeepAlive = true;
};

exports.closeWorkerPool = () => {
  setTimeout(() => {
    WorkerPool.close();
  }, constants.workerCloseDelay);
};

setInterval(() => {
  const workerStats = WorkerPool.getInstrumentationMetric();
  statProcessor.getMetricsForHootHoot(workerStats);
}, constants.push_node_hooothoot_interval);

setInterval(clearSemaphore, constants.clearSemaphoreInterval, "session_stop_semaphore");

// Last process to set the Redis key (across all processes on this machine and this region)
// Will own the Redis metrics.
helper.ownRedisMetrics();

/*
  In order to be able to distinguish between response from
  hub and the jar. We will be attaching an extra header
  'Source-From' with the following values:
  01 => Response from HUB
  02 => Response from JAR
  nil => Request didn't reach HUB
  These will help in grepping nginx logs and make the logs more
  helpful.
*/


function requestHandler(request, response) {
  request.id = request.request_id;
  // adding data to socket for hub timeouts
  request.socket.userPath = request.url;
  constants.pushToHootHootRegistry['requestReceivedCount'] = (constants.pushToHootHootRegistry['requestReceivedCount'] || {});
  constants.pushToHootHootRegistry['requestReceivedCount'][constants.hubName] = (constants.pushToHootHootRegistry['requestReceivedCount'][constants.hubName] || 0) + 1;

  // Setting header for hub response
  if (!response.headersSent) {
    response.setHeader('Source-From', constants.NGINX_SOURCE_FROM_CODES.HUB);
  }

  // If keep-alive enabled + hub deployed, ask Nginx to close connections with old workers else they won't die for some time
  if(constants.shouldDieKeepAlive) response.setHeader('Connection', 'close');

  const xRequestsDebuggerHeader = request.headers['x-requests-debugger'] ? `x-requests-debugger: ${request.headers['x-requests-debugger']}` : '';
  if (request.url != '/wd/hub/session' && request.url.indexOf('/wd/hub/session/') == -1) {
    HubLogger.newCGLogger('REQUEST_START', `Request received - ${request.url} ${xRequestsDebuggerHeader}`, LL.REQUEST, 'non_selenium_session_request');
  }
  if(blocked(request, response)) {
    return;
  }

  helper.addToConsoleTimes(request, 'received-request');
  response.on('close', () => {
    if (helper.isStartRequest(request)) {
      helper.removeFromQueueingStatsHoothoot(request.id);
    }
  });
  response.on('finish', function () {
    if (helper.isStartRequest(request)) {
      helper.removeFromQueueingStatsHoothoot(request.id);
    }

    if (helper.isStopRequest(request)) {
      request.sessionId = url.split('/')[4];
    }

    helper.markRequestEnd(request);
  });

  response.on('error', function(err) {
    var stackTrace = err.stack.toString();
    var basic_auth = '';
    try {
      var auth = Buffer.from(request.headers['authorization'].split(' ')[1], 'base64');
      basic_auth = auth.toString('ascii').split(':');
    }
    catch(e) {
      HubLogger.miscLogger('Misc Error', 'Can\'t get the user name/ key for the user  ' + e, LL.WARN);
    }
    HubLogger.exceptionLogger('ClientResponseError', `[${request.url}] Error sending the response back to the client  ${err}  ${stackTrace}  ${basic_auth}`);
  });
  var url = request.url;


  if (isReqFromBrowserstack(request.headers)) {
    response.setHeader('Access-Control-Allow-Origin', '*');
    response.setHeader('Vary', 'Origin');
    response.setHeader('Access-Control-Allow-Headers', 'Cache-Control, Pragma, Origin, Authorization, Content-Type, Keep-Alive');
    response.setHeader('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
    if (request.method == 'OPTIONS') {
      response.end();
      return;
    }
  }

  if(url == '/health_check_v2'){
    healthCheck.checkHubsHealth((finalResponse) => {
      const responseCode = finalResponse.status == "pass" ? 200 : 503;
      response.writeHead(responseCode, {'content-type': 'application/json; charset=utf-8', 'accept': 'application/json'});
      response.end(JSON.stringify(finalResponse));
    });
    return;
  }

  if(url == '/health_check') {
    response.end();
    return;
  }

  const match = router.match(request.method, url);

  if (match) {
    match.handler(request, response);
    return;
  }

  if (url == '/wd/hub/xdrpc') {
    return handleXDRPC(request, response);
  }

  if(url.indexOf('/wd/hub/status') > -1){
    response.writeHead(200, {'content-type': 'application/json; charset=utf-8', 'accept': 'application/json', 'WWW-Authenticate': 'Basic realm="BrowserStack Selenium Hub"'});
    response.end(JSON.stringify(constants.hub_status_data));
    return;
  }

  if(url.indexOf('/wd/hub/sessions') > -1){
    if (request.headers['authorization']){
      const userAgent = getUserAgent(request);
      var auth = Buffer.from((request.headers['authorization'].split(' ')[1] || ''), 'base64');
      var basic_auth = auth.toString('ascii').split(':');
      var bs_url = `&get_user_sessions_list=true&user_id=${encodeURIComponent(basic_auth[0])}&password=${encodeURIComponent(basic_auth[1])}&user_agent=${encodeURIComponent(userAgent)}`;
      response = chunkedResponse(response);
      browserstack.postBrowserStack(bs_url, {}, request, response);
    } else {
      response.writeHead(401);
      response.end();
    }
    return;
  }

  if(url.indexOf('/post_bs_check_url_data') > -1){
    handleCheckUrlReq(request, response);
    return;
  }

  if(url.indexOf('/callback_done') > -1) {
    var cbquery = nodeurl.parse(url, true).query;
    if(cbquery['sessionId']) {
      handleCallbackDone(request, response, cbquery['sessionId'], cbquery['user'], cbquery['terminal']);
    }
    return;
  }

  if(url.indexOf('/session') < 0) {
    return sessionNotFound(response, false, 'Invalid URL');
  }

  try {
    if (request.headers['connection'] && request.headers['connection'].toLowerCase() == 'close'){
      delete request.headers['connection'];
    }
    return handleRequest(request, response);
  } catch(err) {
    HubLogger.exceptionLogger('EXCEPTION IN HUB REQUEST HANDLER: ' + err.stack.toString(), '', url);
    response.writeHead(200, {'content-type': 'application/json; charset=utf-8', 'accept': 'application/json', 'WWW-Authenticate': 'Basic realm="BrowserStack Selenium Hub"'});
    let dataToSend = JSON.stringify({value: {message: 'Uncaught Exception from BrowserStack Hub'}, sessionId: '', 'status': 13});
    HubLogger.instrumentationStats('Exception Hub Request', {}, '', dataToSend);
    helper.respondWithError(request, response, dataToSend);
  }
}

var server = http.createServer(function(request, response) {
  // Assign a random string to each request for logging purpose
  Chitragupta.setupServerLogger(loggerjs.customLogger, request, response, null, requestHandler);
});
server.keepAliveTimeout = 300 * 1000; /* > 60s (current keep-alive timeout set on Nginx upstream) */
server.headersTimeout = 355 * 1000; /* > keepAliveTimeout because of https://stackoverflow.com/a/68922692 + https://shuheikagawa.com/blog/2019/04/25/keep-alive-timeout/ */
server.timeout = 300 * 1000;

server.on('clientError', (err, socket) => {
  HubLogger.exceptionLogger(`Client Error Bad Request ~ ${err.toString()}`);
  if (err.code === 'ECONNRESET' || !socket.writable) {
    return;
  }
  socket.removeAllListeners("error");
  socket.write([
    'HTTP/1.1 400 Bad Request',
    'Connection: close'
  ].join('\n') + '\r\n\r\n','UTF-8',() => {
    socket.end();
  });
});

const wsHandler = new WebSocketHandler({ noServer: true });
server.on('upgrade', (req, socket, head) => {
  if (req.headers['x-nginx-out-time']) {
    req.nginxToHubTime = Date.now() - parseInt(req.headers['x-nginx-out-time'], 10);
  }

  if (req.headers['x-rtt']) {
    req.userToNginx = parseInt(req.headers['x-rtt'], 10);
  }

  HubLogger.miscLogger('WebSocket', `Received socket upgrade request url ${req.url}`, LL.INFO);
  WebSocketHandler.checkSocketUpgrade(req, (err, jsonData) => {
    if (err) {
      HubLogger.exceptionLogger('Socket upgrade request verification failed', null, req.url, err.toString(), err.stack);
      socket.write('HTTP/1.1 401 Unauthorized\r\n\r\n');
      socket.destroy();
      return;
    }
    HubLogger.miscLogger('WebSocket', `Verified socket upgrade request url ${req.url} product ${req.product}`, LL.INFO);
    wsHandler.handleUpgrade(req, socket, head, {
      data: jsonData,
    });
  });
});

const pushHubServerTimeoutDataToZombieAndHH = () => {
  const kind = 'hub-server-timeout';
  helper.PingZombie({
    kind,
    data: process.pid,
    region: constants.region,
    machine: constants.osHostName,
  });
  // group by genre, sum(value) - hh query
  helper.hoothootPusher(kind, 1, {genre: `pid_${process.pid}`});
};

exports.pushHubServerTimeoutDataToZombieAndHH = pushHubServerTimeoutDataToZombieAndHH;

const timeoutCb = (socket) => {
    if( socket != null || typeof socket !== 'undefined') {
      try {
          HubLogger.miscLogger('Server Timeout','DATA: ' + util.inspect(socket), LL.INFO);
          // Added this alerting to get a rough idea on how many times we get server timeouts
          // On server timeouts, the nginx marks upstream as unhealthy.
          pushHubServerTimeoutDataToZombieAndHH();
          socket.destroy();
      } catch(err) {
          HubLogger.exceptionLogger('Exception on logging details on Server Timeout' + err.toString());
      }
    }
};
// For testing cb triggered on 'timeout' event
exports.timeoutCb = timeoutCb;

HubLogger.miscLogger('Memcache', 'Starting hub with cache: ' + util.inspect(constants.timeout_registry), LL.DEBUG);

server.on('timeout', data => timeoutCb(data));

//ignore connect requests - https://stackoverflow.com/a/34971131
server.on('connect', function(res, socket) {
  socket.write('HTTP/' + res.httpVersion + ' 404 Not Found\r\n\r\n', 'UTF-8', function() {
    socket.end();
  });
});

server.listen(constants.SERVER_PORT, function() {
  HubLogger.miscLogger('Hub', 'Hub listening on ' + constants.SERVER_NAME + ':' + constants.SERVER_PORT, LL.INFO);
});
exports.server = server;
initIdleTimeoutSub();
initSessionDeletionSubscriber();
initUpdateKeyObject();
initCallbackStop();
initQueuePop();

function initIdleTimeoutSub() {
  pubSub.subscribe(constants.sessionTimeout, function(session) {
    const sessionHash = constants.global_registry[session];
    if(sessionHash) {
      setTimeout(() => {sessionManagerHelper.removeFromMemory(sessionHash);}, 15000);
    }
  });
}
exports.initIdleTimeoutSub = initIdleTimeoutSub;

function initSessionDeletionSubscriber () {
  pubSub.subscribe(constants.sessionDeletionChannel, function (session) {
    const sessionHash = constants.global_registry[session];
    if(sessionHash) {
      sessionManagerHelper.removeFromMemory(sessionHash);
    }
  });
}
exports.initSessionDeletionSubscriber = initSessionDeletionSubscriber;

function initUpdateKeyObject() {
  pubSub.subscribe(constants.updateKeyObject, function(message) {
    var obj = JSON.parse(message);
    var sessionId = obj.session || obj.s;
    sessionManagerHelper.getKeyObject(sessionId)
    .then((keyObject) => {
      if(typeof keyObject == 'undefined') return;
      var changed = obj.changed || obj.c;
      for (var key in changed) {
        keyObject[constants.subscribeKeyCodes[key] || key]  = changed[key];
        if(key == 'uploaderKey') ha.setData(keyObject.rails_session_id, keyObject);
      }
    });
  });
}

function initCallbackStop() {
  pubSub.subscribe(constants.callbackStopDone, function(message) {
    var data = JSON.parse(message);
    if(typeof(constants.callback_registry[data.session]) == 'undefined') return;
    HubLogger.miscLogger('callbackStop', 'Found callback for session: ' + data.session + 'req_data: ' + data.req_data, LL.INFO);
    var callback = constants.callback_registry[data.session];
    callback(data.req_data ? data.req_data : '{}');
    delete constants.callback_registry[data.session];
    helper.popNextSessionFromQueue(data);
  });
}

function initQueuePop() {
  pubSub.subscribe(constants.userQueuePop, function(message) {
    var data = JSON.parse(message);
    if (typeof (constants.user_queue_registry[data.user]) == 'undefined' || constants.user_queue_registry[data.user].indexOf(data.queue_id) < 0) return;
    var callback = constants.user_queue_registry['callbacks'][data.queue_id];
    if (callback === undefined) return;
    HubLogger.miscLogger('User Queue', 'Found ' + data.queue_id + ' for user ' + data.user, LL.INFO);
    callback();
  });
}

function handleGetSessionData(response, key){
  var session_data = constants.global_registry[key];
  if (session_data) {
    HubLogger.miscLogger('get_session_data', `Session Data found for ${key} on node with pendingDelete ${session_data['pendingDelete']}`, LL.DEBUG, session_data.debugSession);
    if (session_data.pendingDelete) {
      response.end('{"found":false}');
      return;
    }
    session_data.found = true;
    response.end(JSON.stringify(session_data));
    helper.sessionRemovedFromRegionHook(session_data,false,key);
  } else {
    HubLogger.miscLogger('get_session_data', `Session Data Not found for ${key}, checking redis`, LL.INFO);
    var callback = function(error, result){
      if(result!=null && result.rails_session_id != null && !result.pendingDelete){
        HubLogger.miscLogger('get_session_data', `Session Data found for ${key} on redis`, LL.INFO, result.debugSession);
        result.found = true;
        response.end(JSON.stringify(result));
        helper.sessionRemovedFromRegionHook(result,false,key);
      } else {
        response.end('{"found":false}');
      }
    };
    ha.getData(key, callback);
  }
}

exports.handleGetSessionData = handleGetSessionData;

function handleCallbackDone(request, response, sessionId, userName, terminal){
  requestlib.readRequest(request)
  .then((req_data) => {
    pubSub.publish(constants.callbackStopDone, {session: sessionId, req_data: req_data, user: userName, terminal: terminal});
    response.end();
  })
  .catch((err) => {
    HubLogger.miscLogger('Error during callback done for session: ' + sessionId, err.toString(), LL.ERROR);
    response.end();
  });
}
exports.handleCallbackDone = handleCallbackDone;

function handleXDRPC(request, response) {
  requestlib.readRequest(request)
  .then((req_data) => {
    var log_data = (req_data && req_data != '')? JSON.parse(req_data) : {};
    var methodName = log_data['method'];
    var pathName = log_data['path'];
    var data = log_data['data']? JSON.stringify(log_data['data']) : '';

    HubLogger.miscLogger('xdrpc', 'Request made for: ' + req_data, LL.INFO);

    var temp_headers = request.headers;
    temp_headers['Access-Control-Allow-Origin'] = '*';
    temp_headers['Access-Control-Allow-Headers'] = 'Cache-Control, Pragma, Origin, Authorization, Content-Type, X-Requested-With, Keep-Alive';
    temp_headers['Content-Length'] = data.length;
    temp_headers['Access-Control-Allow-Methods'] = 'GET, PUT, POST, DELETE';
    temp_headers['accept'] = 'application/json';

    requestlib.call({
      method: methodName,
      path: ('/wd/hub' + pathName),
      hostname: constants.SERVER_NAME,
      headers: temp_headers,
      port: constants.SERVER_PORT,
      body: data,
    }).then((res) => {
      response.writeHead(res.statusCode, res.headers);
      response.end(res.data);
    }).catch((err) => HubLogger.miscLogger('Error during response on XDRPC', err.toString(), LL.ERROR));
  });
}
exports.handleXDRPC = handleXDRPC;

function handleCheckUrlReq(request, response) {
  HubLogger.miscLogger('handleCheckUrlReq', 'url: ' + request.url + 'method: ' + request.method, LL.DEBUG);
  requestlib.readRequest(request)
  .then((data) => {
    HubLogger.miscLogger('handleCheckUrlReq', 'url: ' + request.url + ', method: ' + request.method + ', data' + data, LL.INFO);
    var parsed_data = {};
    try{
      parsed_data = JSON.parse(data);
    } catch(err){
      HubLogger.miscLogger('handleCheckUrlReq', 'error while parsing JSON data: ' + data, LL.WARN);
    }
    supporting.modifySecondaryStateForTest(parsed_data.data, parsed_data.sessionId, constants.global_registry[parsed_data.sessionId] && constants.global_registry[parsed_data.sessionId]['secondaryStateOptions']);
    response.end();
  });
}

exports.handleCheckUrlReq = handleCheckUrlReq;

function checkIfSessionOnOtherHubs(key, request, response, host_params, hub_no){
  // NOTE: Any update to this function should be reflected in the same function in seleniumHandler.js -> checkIfSessionOnOtherHubs
  var hub_server = constants.other_hub_servers[hub_no];
  if(!hub_server){
    HubLogger.miscLogger('SESSION_NOT_FOUND', `OtherHubsSessionCheck Hub server is null. Key: ${key}`, LL.INFO);
    sessionNotFound(response, host_params, 'No Hub Contains the Session during Forward Request');
  } else {
    fetchSessionDataFromOtherHub(hub_server, key, request, response, host_params, hub_no);
  }
}

  // NOTE: Any update to this function should be reflected in the same function in seleniumHandler.js -> checkIfSessionOnOtherHubs

function fetchSessionDataFromOtherHub(hub_addr, key, request, response, host_params, hub_no){
  HubLogger.miscLogger('OtherHubsSessionCheck', 'Fetching data from other hub for session: ' + key, LL.INFO);
  requestlib.call({
    hostname: hub_addr,
    path: `/session/get_session_data?sessionId=${key}`,
    timeout: 20000,
    retries: 2
  }).then((get_response) => {
    var response_data = get_response.data;
    try {
      var response_object = JSON.parse(response_data);
      if(response_object.found){
        sessionManagerHelper.refreshAndAddKeyObjectToThisHub(key, hub_addr, response_object);
        return forwardRequest(request, response, response_object, false);
      }
    } catch(err) {
      HubLogger.miscLogger('OtherHubsSessionCheck', 'SessionId: ' + key + ' err: ' + err.toString() + ' data received is: ' + response_data, LL.WARN);
    }
    return checkIfSessionOnOtherHubs(key, request, response, host_params, (hub_no+1));
  }).catch((e) => {
    HubLogger.exceptionLogger(e, hub_addr, '/session/get_session_data/');
    switch (e.type) {
      case 'RequestError':
        HubLogger.miscLogger('', 'Error while getting data from remote hub to this hub: result' + e, LL.WARN);
        break;
      case 'ResponseError':
        HubLogger.miscLogger('OtherHubsSessionCheck', 'Error fetching data from remote hub to this hub: result' + e, LL.WARN);
        break;
      default:
        HubLogger.miscLogger('OtherHubsSessionCheck', 'For Session ' + key + ' Error fetching data from ' + hub_addr + ' to this hub: result' + e, LL.INFO);
    }
    return checkIfSessionOnOtherHubs(key, request, response, host_params, (hub_no+1));
  });
}


function handleRequest(request, response) {
  var url = request.url;
  var client_ip = getClientAddress(request);
  const userAgent = getUserAgent(request);
  const seleniumStartTime = Date.now();

  if (request.method == 'POST' && url == '/wd/hub/session') {
    helper.addToConsoleTimes(request, 'session-creation');
    const requestReceivedAt = new Date();
    request.requestReceivedAt = requestReceivedAt;
    return getHostNameAndCapabilities(request, response);
  }

  var key = url.split('/')[4];
  HubLogger.newCGLogger('REQUEST_START', `Selenium Session request received - ${request.method}:${request.url}`, LL.REQUEST, key);

  if (key === '') {
    response.end();
    return;
  }

  // adding data to socket for hub timeouts
  request.socket.sessionId = key;

  var host_params = constants.global_registry[key];

  constants.execution_time_registry[key] = {
    "startTime" : seleniumStartTime
  };
  if(host_params) {
    helper.updateOutsideBrowserstackTime(host_params);
    helper.timeoutManagerClearTimeout(key, host_params);
    helper.updateAppiumDesktopUsage(host_params, userAgent);
    HubLogger.miscLogger('', host_params['user'] + ' : ' + client_ip + ' : ' + request.method + ' ' + request.url, LL.DEBUG);
    return forwardRequest(request, response, host_params);
  } else {
    HubLogger.miscLogger('Memcache', 'Session not found here checking Memcache : ' + client_ip + ' : ' + request.method + ' ' + request.url, LL.INFO);
    ha.getData(key, function(error, results) {
      host_params = results || undefined;
      if(host_params) {
        sessionManagerHelper.removeFromMemory(host_params);
        constants.global_registry[host_params.rails_session_id] = host_params;
        sessionManagerHelper.recreateRegistry(host_params);
        helper.updateOutsideBrowserstackTime(host_params);
        helper.timeoutManagerClearTimeout(key, host_params);
        helper.updateAppiumDesktopUsage(host_params, userAgent);
      }
      return forwardRequest(request, response, host_params, key);
    });
  }
}

function forwardRequest(request, response, host_params, key) {
  if (!host_params) {
    HubLogger.miscLogger('forwardRequest', `Host_params missing for request ${request.url} session_id ${key}`, LL.INFO);
    if (key){
      checkIfSessionOnOtherHubs(key, request, response, host_params, 0);
    } else {
      if(request.url) {
        HubLogger.miscLogger('SESSION_NOT_FOUND', 'Session not started or terminated for ' + request.url.toString() + ' because Key does not exist in forwardRequest', LL.WARN);
      }
      sessionNotFound(response, host_params, 'Session ID Not Provided in URL');
    }
    return;
  }

  key = host_params.rails_session_id;
  const keyObject = constants.global_registry[key];

  // Save commands to redis for AI
  if(keyObject.aiEnabledSessions && !keyObject.appTesting){
    AICommandHandler.pushCommandRedis(keyObject, request);
  }

  if(host_params && constants.execution_time_registry[key] && constants.execution_time_registry[key]['startTime']){
    const seleniumStartTime = constants.execution_time_registry[key]['startTime'];
    const nginxOutTime = request.headers['x-nginx-out-time'] ? request.headers['x-nginx-out-time'] * 1000 : seleniumStartTime;
    const userToNginxTime = request.headers['x-rtt'] ? (request.headers['x-rtt'] / 1000) : 0;
    helper.startSeleniumCommandClock(key, request.url, request.method, host_params.seleniumRequestsCount || 0, host_params.appTesting, helper.isW3C(host_params),  host_params.group_id, host_params.terminal_type, seleniumStartTime, nginxOutTime, 0, userToNginxTime);
  }

  if(key && keyObject){
    if (host_params.pendingDelete) {
      HubLogger.miscLogger('SESSION_NOT_FOUND', 'Session not started or terminated for ' + key + ' because pendingDelete in forwardRequest', LL.WARN);
      sessionNotFound(response, host_params, 'Machine in Cleaning State While Forwarding Request to Selenium');
      return;
    }
    tracker.countSeleniumRequests(key);

    if(keyObject.appTesting){
      request.is_app_automate_session = true;
    }

    // We only check the 'X-connection-requests' header-field sent to us by Nginx which represents the total conenctions currently being sent through the current socket.
    // We only check for the first occurence where the  'X-connection-requests' value is >= 2 and save it to global keyObject.
    helper.setClientConnectionSocketsCount(request, keyObject, key);

    if(request.is_app_automate_session) {
      const tokens = request.url.split('/');
      let command = `${request.method}:${tokens[tokens.length - 1]}`;
      if(constants.APP_AUTOMATE_APPIUM_COMMANDS_TO_COUNT[command])
      {
        tracker.countAppiumCommandUsage(host_params.rails_session_id, command);
      }
    }
  }

  response = chunkedResponse(response, keyObject.noBlankPolling);

  if (request.method == 'DELETE' && request.url.match(/\/wd\/hub\/session\/[^/]*$/)) {
    let stopHandler = new StopSessionHandler(request, response, keyObject);
    stopHandler.handle(key);
  } else {
    createBridgeClientAndNode(host_params, request, response, {});
  }
}

function getFileNameForMobile(obj) {
  obj.screenshot_counter += 1;
  return obj.s3bucket + '##' + obj.rails_session_id + '##screenshot-' + helper.randomID(5) + '.jpeg';
}

// @description: Checks whether the request is supported for particular platform or not. Uses hash to optmise lookup.
function checkIfRequestIsNotValid(hash) {
  return constants.not_allowed_requests.indexOf(hash) > -1;
}
exports.checkIfRequestIsNotValid = checkIfRequestIsNotValid;

function checkIfNonPipeUrl(hash) {
  return constants.nonPipeUrls.indexOf(hash) > -1;
}
exports.checkIfNonPipeUrl = checkIfNonPipeUrl;

function isPageLoadTimeoutError(parsed_data){
  return parsed_data.status === 28;
}
exports.isPageLoadTimeoutError = isPageLoadTimeoutError;

function checkPossibleToProceed(keyObject, request, response, params, callback){
  params['data'] = '{"state":"success","sessionId":"'+keyObject.rails_session_id+'","value":null,"class":"org.openqa.selenium.remote.Response","status":0}';
  if (!keyObject.appTesting && keyObject.browser.match(/internet explorer|chrome|firefox|safari|android|microsoftedge|iphone|ipad|chrome_android|samsung/i)) {
    return supporting.checkPageLoadOnTerminal(keyObject, function(data, output){
      params['output'] = output;
      HubLogger.seleniumStats('automate-so-timeout-overcome', keyObject, 'Post execute success complete or interactive, going forward', params['data'], 'selenium-command-error-prevention');
      pubSub.publish(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: { instable: true },
      });
      return processResponse(request, response, keyObject, params);
    }, function(err, output){
      if(typeof output == 'undefined'){
        callback();
      }
      else {
        params['output'] = output;
        HubLogger.seleniumStats('automate-so-timeout-overcome', keyObject, 'Post execute success loading, going forward', params['data'], 'selenium-command-error-prevention');
        pubSub.publish(constants.updateKeyObject, {
          session: keyObject.rails_session_id,
          changed: { instable: true },
        });
        return processResponse(request, response, keyObject, params);
      }
    });
  }
  else {
    callback();
  }
}
exports.checkPossibleToProceed = checkPossibleToProceed;

const checkFailureReason = (keyObject, request, response, attempt, indexCounter, failureReason, err) => {
  const { rails_session_id: sessionId, name: terminal, node_died: nodeDied, lastRequest } = keyObject;
  const msgForReason = {
    [ERROR.HUB_TIMEOUT]: 'Hub Timeout',
    [ERROR.NETWORK_DOWN]: 'Network Down',
    [ERROR.FFFF_DOWN]: '5555 Down',
    [ERROR.CONN_FAILED_TWICE]: 'Connection Failed Twice',
    [ERROR.BROWSER_DIED]: 'Browser Died'
  };

  if(nodeDied) {
    failureReason = ERROR.BROWSER_DIED;
  } else if(attempt === 3) {
    failureReason = ERROR.HUB_TIMEOUT;
  } else if (err && err.code === 'ECONNREFUSED') {
    failureReason = ERROR.FFFF_DOWN;
  }

  keyObject.screenshot_counter += 1;
  err = msgForReason[failureReason] ? new Error(msgForReason[failureReason]) : err;

  const tokens = request.url.split('/');
  keyObject.lastRequestDetails = `${request.method}:${tokens[tokens.length - 1]}::${lastRequest || ''}`;

  const randomID = helper.randomID(5);
  helper.takeScreenshotAndUpload(keyObject, randomID, { isError: true });

  let completeFileName = getCompleteFileName(keyObject, randomID);
  if( keyObject && keyObject.os && constants.DEBUGSCREENSHOT_DEPRECATED_OS.includes(keyObject.os.toLowerCase()) ) {
    completeFileName = undefined;
  }
  const sessionRegistry = browserstack.getLoggingOptsRegistry(constants.global_registry[sessionId]);
  const sessionOptions = browserstack.appendOpts(sessionRegistry, { indexCounter });
  HubLogger.nodeErrorHandler(request, response, err, terminal, sessionId, undefined, completeFileName, helper.getDate(), sessionOptions, undefined);
};
exports.checkFailureReason = checkFailureReason;

function isDeleteRequest(request){
  return request.method == 'DELETE' && request.url.match(/\/wd\/hub\/session\/[^/]*$/);
}

// All response/exceptions from the jar are handled in this method
function processResponse(request, response, keyObject, params){
  var data = params.data,
    parsed_data = params.parsed_data,
    output = params.output || {headers:constants.CHUNKED_HEADER, statusCode: 200},
    remoteSessionID = params.remoteSessionID,
    clientSessionID = params.clientSessionID,
    index_counter = params.index_counter,
    callbacks = params.callbacks,
    hostname = params.hostname,
    originalUrl = params.originalUrl;

  const isScreenShotRequest = request.url.match(/\/screenshot$/);

  if (isReqFromBrowserstack(request.headers)) {
    output.headers['Access-Control-Allow-Origin'] = '*';
    output.headers['Vary'] = 'Origin';
    output.headers['Access-Control-Allow-Headers'] = 'Cache-Control, Pragma, Origin, Authorization, Content-Type, Keep-Alive';
    output.headers['Access-Control-Allow-Methods'] = 'POST, GET, OPTIONS';
  }

  if(keyObject.debugSession){
    HubLogger.miscLogger('processResponse', `response data size ${keyObject.rails_session_id} ${data && data.length}`, LL.DEBUG, keyObject.debugSession);
  }

  var keyObjectDiffHash = {};

  var publishKeyObjectChanges = () => {
    pubSub.publish(constants.updateKeyObject, {
      session: keyObject.rails_session_id,
      changed: keyObjectDiffHash
    });
  };

  if(data && data.length == 0)
    data = JSON.stringify(Object.assign({}, constants.SUCCESS_RESPONSE, {'hCode': '13579246'}));

  let osVersion = helper.getOsVersion(keyObject.deviceName);

  if(!isScreenShotRequest) { // TAG: SCREENSHOT-OPTIMIZE
    data = replaceSessionID(data, remoteSessionID, clientSessionID).toString();
  }

  let jsonData = parsed_data || helper.getParsedObjectOrEmpty(data);

  // TAG: SCREENSHOT-OPTIMIZE
  // Idea is, the response of screenshot request does not contain original session id
  // so by this we are saving string.replace and buffer.new i.e 2 copies of response payload
  // If JSON payload has more than 1 key, we are instrumenting it and continue the original flow
  if (isScreenShotRequest && jsonData && Object.keys(jsonData).length > 1) {
    HubLogger.miscLogger('SCREENSHOT-OPTIMIZE', 'Screenshot request for: ' + request.url + ' SessionID: ' + keyObject.rails_session_id + ' keys: ' + Object.keys(jsonData), LL.INFO);
    data = replaceSessionID(data, remoteSessionID, clientSessionID).toString();

    // we don't need to handle exception raised by getParsedObjectOrEmpty, in case of exception we get {}
    jsonData = helper.getParsedObjectOrEmpty(data);
  }
  if(constants.global_registry[keyObject.rails_session_id] && keyObject.appTesting && helper.isAppleOs(keyObject.os) && osVersion >= 14.5 && request.method != 'DELETE' && data) {
    if (jsonData === null) {
      jsonData = helper.getParsedObjectOrEmpty(data);
    }
    if(helper.isW3C(keyObject)){
      if(helper.nestedKeyValue(jsonData, ["value"]) && helper.nestedKeyValue(jsonData["value"], ["error"]) && browserstackErrorUtil.detectMissingAppIdKey(jsonData.value.message)){
        jsonData.value.message += errorMessages.APP_AUTOMATE_MISSING_APP_ID_KEY_ERROR.replace("<current_device_name>", keyObject.deviceName);
      }
    } else {
      if(helper.nestedKeyValue(jsonData, ["status"]) && jsonData.status === 13 && helper.nestedKeyValue(jsonData, ["value"]) && browserstackErrorUtil.detectMissingAppIdKey(jsonData.value.message)){
        jsonData.value.message += errorMessages.APP_AUTOMATE_MISSING_APP_ID_KEY_ERROR.replace("<current_device_name>", keyObject.deviceName);
      }
    }
  }

  jsonData = mapResponseToAppropriateDialect(keyObject, jsonData, 'object');  // map response according to client protocol

  let truncatePerformanceJson = false;
  const kafkaLogType = {};
  if(jsonData['value'] &&  helper.isHash(jsonData['value'])){
    truncatePerformanceJson = helper.isDefined(jsonData['value']['truncatePerformanceJson']);
    delete jsonData['value']['truncatePerformanceJson'];

    // TAG: SCREENSHOT-OPTIMIZE-DELETE-FILE
    delete jsonData['value']['file'];

    if(helper.isDefined(jsonData['value']['performanceLogs'])){
      kafkaLogType.logType = LOG_TYPE.PERFORMANCE;
    }
    delete jsonData['value']['performanceLogs'];
  }
  /* Start: Adding additional feature logs to raw logs */
  if(keyObject.aiHealingCommand == true && isNotUndefined(keyObject.aiSessionDetails) && (isTrueString(keyObject.aiSessionDetails.enable_ai_healing) || isTrueString(keyObject.aiSessionDetails.ai_soft_heal))) {
    delete keyObject.aiHealingCommand;
    let other_flags_to_raw_logs = {};
    if(isTrueString(keyObject.aiSessionDetails.ai_soft_heal)){
      kafkaLogType.logType = LOG_TYPE.AI_SOFT_HEAL;
    } else {
      kafkaLogType.logType = LOG_TYPE.AI_HEAL;
    }
    if(isTrueString(keyObject.aiSessionDetails.isHealSkipped)){
      delete keyObject.aiSessionDetails.isHealSkipped;
      other_flags_to_raw_logs.isHealSkipped = true;
    }
    kafkaLogType.data = AICommandHandler.retrieveRawLogData(jsonData, other_flags_to_raw_logs);
  }
  /* End: Adding additional feature logs to raw logs */

  var deleteOptions = {};
  var hh = output.headers;
  if(output.headers && ['deflate', 'gzip', 'x-gzip'].indexOf(output.headers['content-encoding']) > -1) {
    delete hh['content-encoding'];
  }
  if(hh && (typeof(hh['Transfer-Encoding']) == 'undefined' || hh['Transfer-Encoding'] != 'chunked'))
    hh['content-length'] = data.length;

  // For Delete request don't do a response.write, because that will match
  // the sent data length with the content length
  // and close the connection if it matches
  // Send response.end after postbrowserstack callback

  keyObject.lastResponseTime = Date.now();
  if (isDeleteRequest(request)) {
    deleteOptions = {
      statusCode: output.statusCode,
      index_counter: index_counter,
      hh: hh,
      data: JSON.stringify(Object.assign({}, constants.SUCCESS_RESPONSE, {'hCode': '13579246'})),
    };
  } else {
    // Setting header for jar response
    output.headers['Source-From'] = constants.NGINX_SOURCE_FROM_CODES.JAR;
    const seleniumStatus = helper.nestedKeyValueGeneric(jsonData, ['status'], 'NOT_AVAILABLE', isNotUndefined);
    keyObject.lastResponseStatus = `${output.statusCode}::${seleniumStatus}`;
    data = Object.keys(jsonData).length > 0 ? JSON.stringify(jsonData) : data;

    if(hh && (typeof(hh['Transfer-Encoding']) == 'undefined' || hh['Transfer-Encoding'] != 'chunked')) {
      hh['content-length'] = Buffer.from(data).length;
    }
    response.writeHead(output.statusCode, output.headers);
    response.write(data.toString());
    // HubLogger.newCGLogger('PROCESS_RESPONSE', `Selenium response for - ${request.url}. Data-Size - ${data.length}`, LL.DEBUG, 'selenium_process_response', keyObject.debugSession, undefined, keyObject.user_id);
  }

  if (keyObject.realMobile && helper.isAppleOs(keyObject.os) && request.method === 'POST' && request.url.match(/\/orientation$/) && isString(request['log_data'])) {
    try {
      const orientationData = JSON.parse(request['log_data']);
      if (['landscape', 'portrait'].includes((orientationData['orientation'] || '').toLowerCase())) {
        keyObject.deviceOrientation = orientationData['orientation'].toLowerCase();
        keyObjectDiffHash.deviceOrientation = keyObject.deviceOrientation;
      }
    } catch (e) {
      HubLogger.exceptionLogger(`iosOrientation-ERROR: Unable to parse log data: ${request['log_data']}`, hostname, request.url);
    }
  }

  var debugScreenshot = false;
  var lpath = request.url.split('/');
  lpath = request.method + '/' + lpath[lpath.length - 1];

  if (keyObject.debug && (constants.commandScreenshot.indexOf(lpath) >= 0) && constants.omitDebugMode.indexOf(keyObject.user) == -1) {
    keyObject.screenshot_counter += 1;
    var randomID = helper.randomID(5);
    debugScreenshot = randomID;
    helper.takeScreenshotAndUpload(keyObject, randomID);
  }
  if (request.url.match(/\/screenshot$/)) {
    keyObject.take_screenshot_counter = ++keyObject.take_screenshot_counter || 1;
    keyObject.screenshot_counter += 1;

    // data = S3UploadHelper.uploadScreenshotToS3(data, keyObject, helper.randomID(5), 'selenium');

    if (!keyObject.skip_screenshot_upload) {
      // TAG: SCREENSHOT-OPTIMIZE
      // uploadScreenshotToS3 handles both string and json data
      // we are sending json data to function so we save 1 copy of JSON.parse
      data = S3UploadHelper.uploadScreenshotToS3(jsonData, keyObject, helper.randomID(5), 'selenium', undefined, 'object').toString();
    }

    // TAG: SCREENSHOT-OPTIMIZE
    // Below code is not required as uploadScreenshotToS3 will send response in string format
    // delete jsonData['file']; will be handled above see tag: SCREENSHOT-OPTIMIZE-DELETE-FILE

    // try {
    //   jsonData = JSON.parse(data);
    //   delete jsonData['file'];
    //   data = JSON.stringify(jsonData);
    // } catch(e) {
    //   response.end();
    //   helper.stopSeleniumClock(keyObject.rails_session_id, request.url, request.method, helper.isW3C(keyObject), constants.SELENIUM_ERROR_CODE, request.log_data);
    //   Object.assign(keyObjectDiffHash, {
    //     lastResponseTime: keyObject.lastResponseTime,
    //     lastRequestTime: keyObject.lastRequestTime,
    //     outsideBrowserstackTime: keyObject.outsideBrowserstackTime,
    //     userHubLatency: keyObject.userHubLatency,
    //     customExecutorInstrumentation: keyObject.customExecutorInstrumentation,
    //     seleniumRequestsCount: keyObject.seleniumRequestsCount,
    //   });
    //   helper.timeoutManagerUpdateTimeout(keyObject.rails_session_id, keyObject);
    //   HubLogger.exceptionLogger('ERROR: Not a JSON response from node: ' + data, hostname, request.url);
    //   HubLogger.seleniumStats('json-data-error', keyObject, e.toString(), 'Node data did not result in a valid json:\n' + data, keyObject.key, 'nodeError');
    //   sendCombinedLogs(request, keyObject, JSON.stringify({status: constants.SELENIUM_ERROR_CODE, state: 'error', data: data}), debugScreenshot, kafkaLogType);
    //   publishKeyObjectChanges();
    //   return;
    // }
  }

  if (data && data != '') {
    delete jsonData['file'];
    // Append console logs to file. For every /log request for :browser logs.
    // Need to do only for Chrome (Dektop/Android), as other implementations are not streaming in nature, so calling /log again will also
    // return older log messages, thus this it will append the same log entry  multiple times.
    if(keyObject && keyObject.consoleLogsEnabled && (keyObject.browser == 'chrome' || keyObject.os == 'android') && (helper.checkResponseStatus(jsonData, keyObject, output.statusCode)) && request.url.match(/log$/) && request.log_data) {
      var logParams = {};
      try {
        logParams = JSON.parse(request.log_data);
      } catch(e) {
        // ignore
      }

      if(logParams.type === 'browser' && jsonData && jsonData.value) {
        helper.appendConsoleLogs(keyObject, JSON.stringify(jsonData.value), jsonData.value);
      }
    }

    const isW3c = keyObject.dialect === 'W3C';
    const w3cError = helper.nestedKeyValue(jsonData, ['value','error']);

    if(!keyObject.appTesting
      && ((isW3c && helper.isDefined(w3cError)) || (!isW3c && jsonData.status !== 0 ))
      ){
        const error = isW3c ? w3cError : jsonData.status;
        // Keep count of non 0 status/ error codes
        // for automate, this is used to track selenium exception count on BQ
        keyObject.nonZeroStatusesCount[error] = (keyObject.nonZeroStatusesCount[error] || 0) + 1;
        keyObjectDiffHash.nonZeroStatusesCount = keyObject.nonZeroStatusesCount;
      }

    if(jsonData.status !== 0) {
      if(keyObject && keyObject.appTesting && keyObject.nonZeroStatusesCount) {
        // Keep count of non 0 status codes for app automate sessions, this is being used for tracking if multiple users are facing issues at same time on app automate
        var status = jsonData.status;
        keyObject.nonZeroStatusesCount[status] = (keyObject.nonZeroStatusesCount[status] || 0 )+ 1;
        keyObjectDiffHash.nonZeroStatusesCount = keyObject.nonZeroStatusesCount;
      }

      var nonZeroStatusKey = helper.getNonZeroRedisKey(request.method, request.url, keyObject.rails_session_id);
      if(keyObject && keyObject.nonZeroIncrementCounters && keyObject.nonZeroIncrementCounters.indexOf(nonZeroStatusKey) < 0) {
        keyObject.nonZeroIncrementCounters.push(nonZeroStatusKey);
        keyObjectDiffHash.nonZeroIncrementCounters = keyObject.nonZeroIncrementCounters;
      }
    }
    if (constants.global_registry[keyObject.rails_session_id] && !constants.global_registry[keyObject.rails_session_id].exceptionEncountered && jsonData.status !== 0 && request.method != 'DELETE'){
      constants.global_registry[keyObject.rails_session_id].exceptionEncountered = true;
    }

    if(constants.global_registry[keyObject.rails_session_id]) {
      Object.assign(keyObjectDiffHash, {
        exceptionEncountered: constants.global_registry[keyObject.rails_session_id].exceptionEncountered,
        lastResponseTime: keyObject.lastResponseTime,
        lastRequestTime: keyObject.lastRequestTime,
        outsideBrowserstackTime: keyObject.outsideBrowserstackTime,
        userHubLatency: keyObject.userHubLatency,
        customExecutorInstrumentation: keyObject.customExecutorInstrumentation,
        seleniumRequestsCount: keyObject.seleniumRequestsCount,
        automate_ai_duration: keyObject.automate_ai_duration,
        automate_ai_success: keyObject.automate_ai_success,
        automate_ai_retry_count: keyObject.automate_ai_retry_count,
        automate_ai_find_element_count: keyObject.automate_ai_find_element_count,
        automate_tcg_duration: keyObject.automate_tcg_duration,
      });
    }
    /*if(jsonData["status"] && jsonData["status"].toString() == "13") {
      helper.takeScreenshotAndUpload(keyObject, keyObject.screenshot_counter, true);
      HubLogger.nodeErrorHandler(request, response, null, hostname, clientSessionID, "Remote browser may have died", getCompleteFileName(keyObject, keyObject.screenshot_counter), originalDate);
      return;
    }*/
    if(constants.global_registry[keyObject.rails_session_id] && jsonData.status == 0 && request.method != 'DELETE' && constants.global_registry[keyObject.rails_session_id].exceptionClass) {
      if (!(request.url.match(/\/screenshot$/) || request.url.match(/\/title$/) || request.url.match(/\/url$/) || request.url.match(/\/source$/) || request.url.match(/\/logs$/) || request.url.match(/\/types$/) || request.url.match(/\/window$/))) {
        constants.global_registry[keyObject.rails_session_id].exceptionClass = null;
        keyObjectDiffHash.exceptionClass = null;
      }
    }

    if(constants.global_registry[keyObject.rails_session_id] && jsonData.status !== 0 && request.method != 'DELETE' && jsonData.value) {
      let changed = {};
      if (jsonData.value.class) {
        changed.exceptionClass = jsonData.value.class;
        changed.exceptionRequest = request.hash || keyObject.lastRequest;
      } else if(jsonData.value.message) {
        changed.exceptionMessage = jsonData.value.message.toString().split(/\r?\n/)[0];
        changed.exceptionRequest = request.hash || keyObject.lastRequest;
      }
      if(Object.keys(changed).length > 0) {
        Object.assign(keyObject, changed);
        Object.assign(keyObjectDiffHash, changed);
      }
    }

    if(constants.global_registry[keyObject.rails_session_id] && keyObject.appTesting && jsonData.status === 13 && request.method != 'DELETE' && jsonData.value) {
      let isEconnrefusedSession = false;
      if (jsonData.value.message)
        isEconnrefusedSession = browserstackErrorUtil.detectEconnrefusedMidSession(jsonData.value.message);
      if (isEconnrefusedSession) {
        HubLogger.nodeErrorHandler(request, response, jsonData.value, hostname, clientSessionID, constants.MID_SESSION_BROWSERSTACK_ERROR_BUCKET.econnrefused, undefined, helper.getDate(), {}, keyObject, undefined);

        publishKeyObjectChanges();
        return;
      }
    }

    // To handle w3c error formats, if the message is empty, populate the message field with the error which will be displayed in the text logs as response.
    const jsonDataMessage = helper.nestedKeyValue(jsonData, ["value", "message"]);
    if(isW3c && !(jsonDataMessage === undefined || jsonDataMessage === null) && jsonDataMessage.length === 0) {
      jsonData['value']['message'] = w3cError;
    }

    var isException = jsonData['value'] && jsonData['value']['message'] && jsonData.status !== 0 && !isDeleteRequest(request);
    if(jsonData['value'] && jsonData['value']['screen'] && (typeof jsonData['value']['screen'] == 'string')) {
      keyObject.screenshot_counter++;
      if(isDeleteRequest(request)){
        delete jsonData['value']['screen'];
      }
      else {
        jsonData = S3UploadHelper.uploadScreenshotToS3(jsonData, keyObject, helper.randomID(5), 'selenium', 'object');
      }
    }
    else if(isException) {
      var isExcludedException = new RegExp(/session\/[^/]*\/(element|elements)$/).test(request.url);
      if(keyObject.debug || !isExcludedException ){
        keyObject.screenshot_counter++;
        randomID = helper.randomID(5);
        helper.takeScreenshotAndUpload(keyObject, randomID, { isError: true });
        if( keyObject && keyObject.os && !constants.DEBUGSCREENSHOT_DEPRECATED_OS.includes(keyObject.os.toLowerCase()) ) {
          jsonData['value']['screen'] = getCompleteFileName(keyObject, randomID);
        }
      } else {
        delete jsonData['value']['screen'];
      }
    }

    if(jsonData['value']){
      var stringifyData = jsonData['value'];
      if(truncatePerformanceJson) {
        HubLogger.miscLogger('truncatePerformanceJson', 'rails_session_id: ' + keyObject.rails_session_id + ', for request.method: ' + request.method + ', request.url: ' + request.url + ' with size: ' + stringifyData.length, LL.INFO);
        jsonData['value']['report'] = 'TRUNCATED';
      }
      if(typeof jsonData['value'] != 'string')
        stringifyData = JSON.stringify(jsonData['value']);
      if(stringifyData.length > 2*1024*1024){
        HubLogger.miscLogger('truncateResponse', 'rails_session_id: ' + keyObject.rails_session_id + ', for request.method: ' + request.method + ', request.url: ' + request.url + ' with size: ' + stringifyData.length, LL.INFO);
        jsonData['value'] = 'TRUNCATED';
      }
    }

    if(jsonData && jsonData['data'] && jsonData['data'].constructor && jsonData['data'].constructor.name == 'Buffer'){
      jsonData['data'] = jsonData['data'].toString();
    }

    if (!callbacks['callbackEnd']) {
      data = JSON.stringify(jsonData);
      sendCombinedLogs(request, keyObject, data, debugScreenshot, kafkaLogType);
    }
  } else {
    if (!callbacks['callbackEnd']) {
      sendCombinedLogs(request, keyObject, data, debugScreenshot, kafkaLogType);
    }
  }
  if (callbacks['callbackEnd']) { callbacks['callbackEnd'](request, response, keyObject, deleteOptions); }

  //HubLogger.requestLogger(hostname, request, keyObject.user, originalUrl, true, originalDate, LL.DEBUG);
  HubLogger.jarRequestLogger(hostname, request, keyObject.user + ' : ' + data + (request.logging_data ? (' LOGGING_DATA ' + JSON.stringify(request.logging_data)) : ''), originalUrl, true, undefined, LL.REQUEST);

  if(!callbacks['callbackEnd'] && constants.global_registry[keyObject.rails_session_id] && constants.global_registry[keyObject.rails_session_id].lastRequestTime) {
    const timeReq = keyObject.lastResponseTime - constants.global_registry[keyObject.rails_session_id].lastRequestTime;
    if(timeReq > 0) {
      if(typeof(constants.global_registry[keyObject.rails_session_id].hubTime) == 'undefined'){
        constants.global_registry[keyObject.rails_session_id].hubTime = timeReq;
      } else {
        constants.global_registry[keyObject.rails_session_id].hubTime += timeReq;
      }
    }
  }

  // End response in callback, not called in case of DELETE session req
  if(!callbacks['callbackEnd']) {
    response.end();
    let isW3C = helper.isW3C(keyObject);
    let statusCode = 0;

    if(isW3C && keyObject.realMobile) {
      let stackTrace = helper.nestedKeyValue(jsonData, ["value"]) ? jsonData["value"]["stacktrace"] : "Success";
      statusCode = w3cHelper.mapW3CErrorToJSONWPStatus(stackTrace);
    } else {
      // jsonData.status can be undefined in case of selenium, we set to as per output status code, e.g.: for element not found 404 is returned.
      statusCode = jsonData.status || ( output.statusCode == 200 ? constants.SELENIUM_SUCCESS_CODE : output.statusCode );
    }

    helper.stopSeleniumClock(keyObject.rails_session_id, request.url, request.method, isW3C, statusCode, request.log_data);
    keyObjectDiffHash.insideHubTime = keyObject.insideHubTime;
    keyObjectDiffHash.hubProcessingTime = keyObject.hubProcessingTime;
    keyObjectDiffHash.nginxToHubTime = keyObject.nginxToHubTime;
    keyObjectDiffHash.userToNginxTime = keyObject.userToNginxTime;
    keyObjectDiffHash.jarTime = keyObject.jarTime;

    checkAndSetLocalNudge(keyObject, output, request, keyObjectDiffHash);

    ha.setData(keyObject.rails_session_id, keyObject);
    helper.timeoutManagerUpdateTimeout(keyObject.rails_session_id, keyObject);
  }

  if (keyObject && keyObject.nudgeLocalNotSetError && ((request.url.match(/\/url$/) && request.method == 'POST') || (request.url.match(/\/title$/) && request.method == 'GET'))) {
    keyObjectDiffHash.nudgeLocalNotSetError = keyObject.nudgeLocalNotSetError;
  }

  if (keyObject) {
    AICommandHelper.evaluateKeyObjectDiff(keyObject, keyObjectDiffHash);
  }

  if (keyObject && keyObject.elementNotFound === constants.ELEMENT_NOT_FOUND_DETECTED) {
    keyObject.elementNotFound = true;
    keyObjectDiffHash.elementNotFound = keyObject.elementNotFound;
  }

  if (keyObject && keyObject.pageLoadError === constants.PAGE_LOAD_ERROR_DETECTED) {
    keyObject.pageLoadError = true;
    keyObjectDiffHash.pageLoadError = keyObject.pageLoadError;
  }

  publishKeyObjectChanges();
}
exports.processResponse = processResponse;

function checkAndSetLocalNudge(keyObject, output, request) {
  if (!keyObject.nudgeLocalNotSetError && !keyObject.userPassedLocalCap) {
    // if user hits a local endpoint without local set to true, gets 500 response
    if (output.statusCode == 500) {
      try {
        const parsedData = JSON.parse(output.data);
        const errMesg = parsedData.value.message;
        if (
          errMesg && !!constants.NUDGE_LOCAL_ERRORS.find((errMsg) => { return errMesg.includes(errMsg);})
        ) {
          const req_data = {
            'url': JSON.parse(request.log_data).url,
          };
          const { requestDataUrlHostname } = helper.getRequestObjAndUrlHostname(
            JSON.stringify(req_data),
            keyObject
          );
          keyObject.nudgeLocalNotSetError = requestDataUrlHostname;
        }
      } catch (e) {
        HubLogger.miscLogger('Error in localNudge processResp', `Session Id: ${keyObject.rails_session_id} localNudge non-safari, stacktrace: ${e}`, LL.WARN);
      }
    }
    // in case of safari we get status 200, even for above case
    // capturing in case of driver.title
    if (helper.nestedKeyValue(keyObject, ['browser']) && keyObject.browser.toLowerCase() === 'safari') {
      try {
        const parsedData = JSON.parse(output.data);
        const errMesg = parsedData.value;
        if (errMesg && (errMesg.toString().includes('Failed to open page') || errMesg.toString().includes('Page not found'))) {
          const req_data = {
            'url': keyObject['lastOpenedUrl'],
          };
          const { requestDataUrlHostname } = helper.getRequestObjAndUrlHostname(JSON.stringify(req_data), keyObject);
          keyObject.nudgeLocalNotSetError = requestDataUrlHostname;
        }
      } catch (e) {
        HubLogger.miscLogger('Error in localNudge processResp', `Session Id: ${keyObject.rails_session_id} localNudge safari, stacktrace: ${e}`, LL.WARN);
      }
    }
  }
}
exports.checkAndSetLocalNudge = checkAndSetLocalNudge;

// @description: Creates bride between selenium node running on terminal and client who is running tests
// function createBridgeClientAndNode(keyObject, request, response, callbacks, attempt, index_counter) {
function createBridgeClientAndNode(keyObject, request, response, callbacks) {
  Chitragupta.setupServerLogger(loggerjs.customLogger, request, response, null, (request, response) => {
  var attempt = arguments[4],
      index_counter = arguments[5],
      payload = arguments[6],
      retryOutput = arguments[7],
      retryData = arguments[8],
      requestData = undefined;
  if (!attempt) {
    attempt = attempt || 1;
  }
  HubLogger.miscLogger('connectionTryOnTerminal', `rails_session_id: ${keyObject.rails_session_id}, attempt: ${attempt}, for request.method: ${request.method}, request.url: ${request.url}`, attempt === 1 ? LL.DEBUG : LL.INFO);
  var splited_url = request.url.split('/');
  var hash = request.method + ':' + splited_url[splited_url.length - 1];
  request.hash = hash;
  if(splited_url.length > 2){
    if(splited_url[splited_url.length - 2] == 'cookie') hash = request.method + ':' + 'cookie';
    if(splited_url[splited_url.length - 2] == 'attribute') hash = request.method + ':' + 'attribute';
  }

  // First check if request is for Android and then check if its valid request for android or not. If valid then carry on else respond with empty json.
  if(keyObject.port == 45693 && checkIfRequestIsNotValid(hash)) {
    // Send a empty json request to user to continue test.
    response.end('{}');
    return;
  }

  if(typeof constants.global_registry[keyObject.rails_session_id] == 'undefined'){
    HubLogger.exceptionLogger('Error while creating bridge to JAR: sessionNotFound, keyObject ' + HubLogger.logKeyObject(keyObject));
    HubLogger.miscLogger('SESSION_NOT_FOUND', 'Session not started or terminated for ' + keyObject.rails_session_id + ' while creating Bridge to JAR.', LL.DEBUG, keyObject.debugSession);
    return sessionNotFound(response, keyObject, 'Session Deleted Before the Current Node Could Make a Bridge');
  }
  var non_pipe_url = checkIfNonPipeUrl(hash);

  var hostname = keyObject.name;
  var rproxyHost = keyObject.rproxyHost;

  // eliminate rproxy for same region - CORE-17212
  if(constants.eliminateRproxyPrivateHubSubRegions && constants.PRIVATE_HUB_REGIONS.has(constants.region) && constants.PRIVATE_HUB_REGIONS_TERMINAL_MAPPING[constants.region]  == keyObject.terminalSubRegion ){
    rproxyHost = hostname;
  }

  HubLogger.miscLogger(
    "rproxy params in createBridgeClientAndNode",
    keyObject.rails_session_id + "rproxyHost: " + rproxyHost + "region :" + constants.region + "host_name :" + hostname + "terminal mapping :" + constants.PRIVATE_HUB_REGIONS_TERMINAL_MAPPING[constants.region] + "terminalSubRegion :" + keyObject.terminalSubRegion + " on port " + keyObject.port,
    LL.INFO
  );

  var port = keyObject.port;
  var remoteSessionID = keyObject.key;
  var clientSessionID = keyObject.rails_session_id;
  var browserstackTunnel = keyObject.tunnel;
  if(attempt == 1 && !callbacks['callbackEnd'] && typeof(request.transitionCheckDone) == 'undefined') {
    var sleepNow = Date.now();
    var sleep = Math.floor((Date.now() - keyObject.lastResponseTime)/1000);
    var numSleep = sleep ? 1 : 0;
    keyObject.request_count = ++keyObject.request_count || 1;
    keyObject.url_changed = (request.method === 'POST' && request.url.indexOf('/url') > -1) ? true : keyObject.url_changed;
    if(constants.global_registry[keyObject.rails_session_id]) {
      pubSub.publish(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          lastRequestTime: keyObject.lastRequestTime,
          outsideBrowserstackTime: keyObject.outsideBrowserstackTime || 0,
          userHubLatency: keyObject.userHubLatency,
          sleepTime: keyObject.sleepTime + sleep,
          sleepTimeNow: sleepNow,
          numSleep: keyObject.numSleep + numSleep,
          request_count: keyObject.request_count,
          seleniumRequestsCount: keyObject.seleniumRequestsCount,
        },
      });
    }
  }

  var originalUrl = request.url;
  var url = replaceSessionID(request.url, clientSessionID, remoteSessionID);
  var headers = request.headers;
  var lpath = request.url.split('/');
  lpath = request.method + '/' + lpath[lpath.length - 1];
  index_counter = index_counter || 0;
  if(keyObject.device && keyObject.debug && (constants.commandScreenshot.indexOf(lpath) >= 0)) {
    headers['browserstack-debug'] = getFileNameForMobile(keyObject);
  }
  if (isDeleteRequest(request)) {
    HubLogger.miscLogger('DELETE_REQUEST', 'Delete request for  ' + keyObject.rails_session_id + ' to JAR.', LL.INFO);
    headers['connection'] = 'close';
    // For python with Appium
    delete headers['content-type'];
  }
  requestlib.appendBStackHostHeader(hostname, headers);

  var output;
  var fileUploadTimeout = undefined;

  var requestStateObj = {
    request: request,
    response: response,
    hash: hash,
    output: output,
    index_counter: index_counter,
    callbacks: callbacks,
    hostname: hostname,
    rproxyHost: rproxyHost,
    originalUrl: originalUrl,
    attempt: attempt,
    clientSessionID: clientSessionID,
    remoteSessionID: remoteSessionID,
    browserstackTunnel: browserstackTunnel,
    payload: payload,
    requestData: requestData,
    non_pipe_url: non_pipe_url,
    retryData: retryData,
    retryOutput: retryOutput,
  };

  if(typeof(request.log_date) == 'undefined') request.log_date = helper.getDate();
  request.log_data = request.log_data || '';
  if (bridge.basicAuthPopupExists(keyObject, requestStateObj)) return;
  if (attempt == 1 && bridge.transitionCheck(keyObject, requestStateObj)) return;
  if (bridge.activeElementCheck(keyObject, requestStateObj)) return;

  if (45693 == port) delete headers['accept-encoding'];

  var req_data_retry;
  var longPageLoadTimeout;
  bridge.loadRequestData(keyObject, requestStateObj)
  .then(async (req_data) => {
    req_data_retry = req_data;
    requestStateObj.req_data = req_data;
    var options = {
      method: request.method,
      path: url,
      headers: headers,
      hostname: rproxyHost,
      port: port,
      body: req_data,
      timeout: constants.NODE_DIED_IN,
    };

    const session_key = keyObject.rails_session_id;
    if (session_key && constants.global_registry[session_key]) {
      const tokens = request.url.split('/');
      let command = `${request.method}:${tokens[tokens.length - 1]}`;
      if (constants.APP_AUTOMATE_APPIUM_COMMANDS_TO_TRACK_DATA[command]) {
        tracker.trackAppiumCommandData(session_key, command, JSON.parse(req_data));
      }
    }

    if(helper.isBlockedRequest(request.url, keyObject['appTesting'])) {
      HubLogger.miscLogger('Blocking request' , 'endpoint blocked : ' + url.toString() + '. session: ' + keyObject.rails_session_id, LL.INFO);
      requestStateObj.data = JSON.stringify({ sessionId: requestStateObj.clientSessionID, status: 13, value: { message: 'Command is not supported'}});
      sendCombinedLogs(request, keyObject, requestStateObj.data, false);
      return bridge.sendErrorResponse(keyObject, requestStateObj);
    }

    if(request.url.match(/session\/[^/]*\/location/) && request.method === 'GET' && helper.isAppleOs(keyObject.os) && keyObject['appium_version'] && (helper.versionCompare(keyObject['appium_version'], '1.20.2') >= 0)) {
      const handler = new IOSGetLocationHandler(keyObject, request, response);
      const res = await handler.processCommand(requestStateObj, url);
      return res.returnData;
    }

    if (checkRequestForRedaction(request, redactRequestEndpoints) && request.method === 'POST' && request.is_app_automate_session && (helper.versionCompare(keyObject['appium_version'], '2.15.0') >= 0)) {
      const sessionData = constants.global_registry[keyObject.rails_session_id];
      if (sessionData && 
          sessionData.maskCommands && 
          Array.isArray(sessionData.maskCommands) && 
          sessionData.maskCommands.includes("setvalues")) {
            markAsSensitive(request);
      } else {
        HubLogger.miscLogger(
          'SensitiveValues',
          `No mask commands detected for session: ${keyObject.rails_session_id}`,
          LL.INFO
        );
      }
    }
  
    // in case of isLockedForAppSettingsFlow, we have settings app automation running on platform
    // hence need to block all parallel requests meanwhile
    // so as to prevent any restricted settings being toggled
    if(keyObject.isLockedForAppSettingsFlow) {
      HubLogger.miscLogger('Blocking request' , 'Command intercepted while session is in locked state: ' + url.toString() + '. session: '  + keyObject.rails_session_id, LL.INFO);
      requestStateObj.data = JSON.stringify({ sessionId: requestStateObj.clientSessionID, status: 13, value: { message: '[BROWSERSTACK_COMMAND_EXECUTION_ERROR]  Command received while app settings automation in progress. Please ensure no command is sent while App Settings automation is in progress. For more details, check our documentation and if the error persists, please reach out to support.'}});
      sendCombinedLogs(request, keyObject, requestStateObj.data, false);
      return bridge.sendErrorResponse(keyObject, requestStateObj);
    }

    if(requestStateObj.hash == 'POST:url' && attempt < 2) {
      var userUrlValue = null;
      var jsonReqData = {};
      var errorfileScheme = false;
      try {
        jsonReqData = JSON.parse(req_data);
      } catch(e) {
        errorfileScheme = true;
        HubLogger.miscLogger('POST:url response check for file:// scheme', 'Cannot JSON parse request data for open url. session: ' + keyObject.rails_session_id, LL.DEBUG, keyObject.debugSession);
      }
      try {
        userUrlValue = urlModule.parse(jsonReqData.url);
      } catch(e) {
        errorfileScheme = true;
        HubLogger.miscLogger('POST:url response check for file:// scheme', 'Cannot url parse for open url. session: ' + keyObject.rails_session_id, LL.DEBUG, keyObject.debugSession);
      }
      if(!errorfileScheme) {
        if(jsonReqData && jsonReqData.url && userUrlValue && userUrlValue.protocol && userUrlValue.protocol == 'file:' ) {
          HubLogger.miscLogger('POST:url response check for file:// scheme', 'Avoiding the url : ' + jsonReqData.url.toString() + '. session: ' + keyObject.rails_session_id, LL.DEBUG, keyObject.debugSession);
          requestStateObj.data = JSON.stringify({ sessionId: requestStateObj.clientSessionID, status: 13, value: { message: 'Opening url with scheme \'file://\' is not supported' } });
          var requestLogline = helper.s3LogFormat('REQUEST', helper.getDate(), '[' + helper.getDate() + '] POST /session/' + keyObject.rails_session_id + '/url') + '\r\n';
          var responseLogline = helper.s3LogFormat('RESPONSE', helper.getDate(), requestStateObj.data.toString('utf-8'));
          HubLogger.sessionLog(keyObject, 'REQUEST_RESPONSE', requestLogline + responseLogline);
          return bridge.sendErrorResponse(keyObject, requestStateObj);
        }
      }
    }
    if(requestStateObj.hash == 'POST:url-https-auth' && attempt < 2) {
      let handler = new UrlHttpsAuthHandler(keyObject, request, response);
      handler.processCommand(requestStateObj, callbacks, attempt, index_counter, req_data_retry);
    } else if(requestStateObj.hash == 'POST:basic-auth-edge' && attempt < 2) {
      const handler = new BasicAuthEdgeHandler(keyObject, request, response);
      const resp = handler.processCommand(req_data, requestStateObj, options);
      if (resp.returnData) return resp.returnData;
      req_data = resp.reqData;
    } else if (requestStateObj.hash === 'POST:ie11w10_file_upload' && attempt < 2) {
      var user_file_json = helper.getParsedObjectOrEmpty(req_data);
      let filePath = '';

      if (Array.isArray(user_file_json.value) && user_file_json.value.length > 0) {
        const filePathSplit = path.win32.normalize(user_file_json.value.join('')).split("\\");
        const fileName = filePathSplit.pop();
        filePath = [...filePathSplit, `"${fileName}"`].join("\\");

        HubLogger.miscLogger(`IE11W10 File Upload path -- ${filePath}`, filePath, LL.DEBUG, keyObject.debugSession);
      }

      fileUploadTimeout = setTimeout(function() {
        fileUploadTimeout = undefined;

        requestlib.call({
          method: 'GET',
          hostname: rproxyHost,
          port: 4567,
          path: '/file_upload_ie11?file_path=' + encodeURIComponent(filePath),
          headers: requestlib.appendBStackHostHeader(hostname),
          recordJarTime: true
        }).then((res) => {
          helper.addToJarTime(keyObject.rails_session_id, res);
          requestStateObj.hash = 'POST:value';
          if(requestStateObj.returned)
            bridge.sendResponse(keyObject, requestStateObj);
          HubLogger.miscLogger('IE11W10 File Upload Returned', `${requestStateObj.clientSessionID} ` + res.data, LL.INFO);
        }).catch((err) => {
          requestStateObj.hash = 'POST:value';
          if(requestStateObj.returned)
            bridge.sendResponse(keyObject, requestStateObj);
          HubLogger.miscLogger('Error during file upload on IE11W10', `${requestStateObj.clientSessionID} ${err.toString()}`, LL.ERROR);
        });
      }, constants.IE11W10_FILE_UPLOAD_TIMEOUT);
    } else if (requestStateObj.hash == 'POST:ie-sendkeys' && attempt < 2) {
      let handler = new IESendKeysHandler(keyObject, request, response, options);
      if (handler.processCommand(requestStateObj)) {
        return;
      }
    } else if(requestStateObj.hash == 'POST:ie-keys' && attempt < 2) {
      let handler = new IEKeysHandler(keyObject, request, response);
      const res = await handler.processCommand(requestStateObj, options);
      if (res.returns) return;
    } else if(requestStateObj.hash == 'POST:ie-jsSendKeys' && attempt < 2) {
      const handler = new JSSendKeysHandler(keyObject, request, response);
      const result = handler.processCommand(requestStateObj, options);
      if(result != null) {
        return result;
      }
    } else if(hash == 'POST:execute' || hash == 'POST:sync'){
      const handler = new ExecuteScriptHandler(keyObject);
      const res = await handler.processCommand(requestStateObj);
      if(res.returns) return;
    } else if (helper.checkDevicesForProxyPolling(keyObject) && /^POST:(url|forward|back|refresh|click|submit)$/.test(hash) && (keyObject.tunnel || keyObject.networkLogs)) {
      const handler = new EdgeProxyPollingCheck(keyObject, request, response);
      await handler.processCommand(requestStateObj);
    }

    let browser_version = parseInt(keyObject.browser_version);
    if (keyObject.appiumandroid && !keyObject.realMobile && request.url.match(/\/orientation$/)) {
      const handler = new AppiumEmulatorOrientationHandler(keyObject, request, response);
      handler.processCommand(requestStateObj);
      return;
    } else if (keyObject.os.match(/mac/i) && request.url.match(/\/maximize$/) && keyObject.browser.match(/chrome/i) && browser_version == 70 && !keyObject.browser_version.match(/beta/i)) {
      const handler = new ChromeMacMaximizeHandler(keyObject, request, response);
      await handler.processCommand(requestStateObj);
      return;
    } else if (keyObject.realMobile && helper.isAppleOs(keyObject.os) && request.url.match(/\/maximize$/)) {
      const handler = new IOSMaximizeHandler(keyObject, request, response);
      await handler.processCommand(requestStateObj);
      return;
    } else if (keyObject.realMobile && keyObject.os != 'android' && request.url.match(/\/file$/)) {
      const handler = new IOSFileUploadHandler(keyObject, request, response);
      const res = await handler.processCommand(requestStateObj);
      return res.returnData;
    } else if (keyObject.realMobile && keyObject.os == 'ios' && parseFloat(keyObject.platformDetails.platformVersion) >= 17 && (/^\/wd\/hub\/session\/.*\/location$/.test(request.url)) && request.method === 'POST') {
      const handler = new IOSLocationHandler(keyObject, request, response);
      const validationErrors = IOSLocationHandler.validateRequestData(JSON.parse(req_data));
      if (validationErrors) {
        requestStateObj.data = JSON.stringify({ sessionId: keyObject.rails_session_id, status: 13, value: { message: `${validationErrors[0]}`}});
        bridge.sendResponse(keyObject, requestStateObj);
        return;
      }
      handler.processCommand(requestStateObj, req_data);
      return;
    } else if (keyObject.realMobile && helper.isAppleOs(keyObject.os) && request.url.match(/\/screenshot$/) && keyObject.deviceName && parseInt(keyObject.deviceName.split('-')[1]) < 10) {
      const handler = new IOSScreenshotHandler(keyObject, request, response);
      const res = await handler.processCommand(requestStateObj);
      if(res.returns) return;
    } else if (request.method == 'POST' && request.url.match(/\/wd\/hub\/session\/.*\/log$/)) {
      // Check if this is a device log request (logcat or syslog)
      let logType = null;
      try {
        const logData = JSON.parse(req_data);
        logType = logData.type;
      } catch (e) {
        // If we can't parse the request data, continue with normal flow
        HubLogger.miscLogger('ParseError', `Session Id: ${keyObject.rails_session_id} Error in parsing request data for log type, stacktrace: ${e}`, LL.DEBUG);
      }

      if ((logType === 'logcat' || logType === 'syslog') && request.is_app_automate_session) {
        const handler = new DeviceLogHandler(keyObject, request, response);
        handler.processCommand(requestStateObj, logData);
        return;
      } else if (keyObject.consoleLogsEnabled && keyObject.browser == 'firefox') {
        const handler = new FirefoxConsoleLogs(keyObject, request, response);
        handler.processCommand(requestStateObj);
        return;
      }
    } else if(keyObject.realMobile && (keyObject.os == 'android') && request.url.match(/\/orientation$/)) {
      const handler = new AndroidOrientationHandler(keyObject, request, response);
      const resp = await handler.processCommand(req_data, requestStateObj, attempt);
      longPageLoadTimeout = resp; // FIXME: remove if internal logic removed
      return;
    } else if (keyObject.realMobile && request.method === 'POST' && request.url.match(/\/reset$/) && keyObject.customFullReset && (keyObject.skipPlatformEnterpriseFlow || keyObject.resignApp || keyObject.os === 'android')) {
      // If reset is invoked and fullReset capability is passed as true
      const handler = new ResetAppHandler(keyObject, request, response);
      handler.processCommand(requestStateObj);
      return;
    } else if (request.method === 'POST' && (request.url.match(/\/app\/strings$/) || helper.isMobileCommand(request.url, req_data, 'mobile: getAppStrings')) && helper.isAppleOs(keyObject.os) ) {
        const handler = new IOSAppStringHandler(keyObject, request, response);
        const res = await handler.processCommand(requestStateObj);
        if (res.returns) return;
    } else if (request.method === 'POST' && helper.isDefined(req_data) && (request.url.match(/\/install_app$/) || helper.isMobileCommand(request.url, req_data, 'mobile: installApp'))) {
      const handler = new InstallAppHandler(keyObject, request, response);
      const resp = await handler.processCommand(requestStateObj, req_data);
      if (resp.returns) { return;}
    } else if (request.method === 'POST' && ( /\/push_file$/.test(request.url) || helper.isMobileCommand(request.url, req_data, 'mobile: pushFile')) && keyObject.realMobile && keyObject.os === 'android' && helper.isDefined(req_data) && request.is_app_automate_session ) {
      // Remove the `is_app_automate_session` condition after product's approval.
      const handler = new AndroidPushFileHandler(keyObject, request, response);
      const resp = handler.processCommand(requestStateObj, req_data);
      if (resp.returns) { return;}
    } else if (keyObject.realMobile && request.method === 'POST' && ( request.url.match(/\/launch$/) || helper.isMobileCommand(request.url, req_data, 'mobile: launchApp')) && keyObject.customFullReset && (keyObject.resignApp || keyObject.os === 'android')) {
      // If driver.launch_app command is used and fullReset capability is passed as true
      let skip = false;
      try {
        if (helper.isMobileCommand(request.url, req_data, 'mobile: launchApp')) {
          const reqDataObj = JSON.parse(req_data);
          if (Array.isArray(reqDataObj.args) && reqDataObj.args[0] && Object.prototype.hasOwnProperty.call(reqDataObj.args[0], 'bundleId')) {
            skip = reqDataObj.args[0].bundleId !== keyObject.bundleId;
          }
        }
      } catch (err) {
        HubLogger.miscLogger('LaunchAppHandler', `Failed to parse JSON Error: ${err}`, LL.INFO);
      }
      if(!skip) {
        const handler = new LaunchAppHandler(keyObject, request, response);
        handler.processCommand(requestStateObj);
        return;
      }
    } else if (request.method === 'POST' && request.url.match(/\/execute$/) && keyObject.os === 'android' && helper.isDefined(req_data) ) {
      const handler = new AndroidMultiApksHandler(keyObject, request, response);
      const resp = handler.processCommand(requestStateObj, req_data);
      if (resp.returns) { return;}
    } else if (/^\/wd\/hub\/session\/.*\/url$/.test(request.url) && request.method === 'GET' && keyObject.sendDummyGetUrlRespToTestcafe) {
      const handler = new TestCafeDummyResponseHandler(keyObject, request, response);
      handler.processCommand(requestStateObj, req_data);
      return;
    } else if (helper.isW3C(keyObject) && keyObject.os !== 'ios' && !keyObject.appTesting && request.method === 'GET' && request.url.match(/\/log\/types$/) && !request.url.match(/\/se\/log\/types$/)) {
      const handler = new GetTypesHandler(keyObject, request, response);
      handler.processCommand(requestStateObj);
      return;
    } else if ((keyObject.os === 'ios' || keyObject.os === 'android') && !keyObject.appTesting && request.method === 'GET' && request.url.match(/\/se\/log\/types$/)) {
      const handler = new GetTypesHandler(keyObject, request, response);
      handler.processCommand(requestStateObj, true);
      return;
    } else if (request.method === 'POST' && request.url.match(/\/keys$/) && !keyObject.appTesting && helper.isW3C(keyObject)) {
      const handler = new W3CSendKeysHandler(keyObject, request, response);
      handler.processCommand(requestStateObj, req_data);
      return;
    } else if (request.url.match(/\/wd\/hub\/session\/[^/]*$/) && request.method === 'GET' && helper.isW3C(keyObject) && !keyObject.appTesting) {
      const handler = new GetSessionHandler(keyObject, request, response);
      handler.processCommand(requestStateObj);
      return;
    } else if (!keyObject.realMobile && !keyObject.appiumandroid && !keyObject.appiumios && request.method === 'GET' && request.url.match(/\/orientation$/)) {
      const handler = new DesktopOrientationHandler(keyObject, request, response);
      handler.processCommand(requestStateObj);
      return;
    } else if (keyObject.realMobile && keyObject.os === 'ios' && !keyObject.appTesting && keyObject.automationName && keyObject.automationName.toString().toLowerCase() === 'safari') {
      // TODO: move to processOptions of base command handler - phase 2
      options = mapSafariDriverRequest(options);
    }

    options.headers = helper.updateHeadersForSelenium4Jars(keyObject, options.headers);
    requestlib.appendBStackHostHeader(hostname, options.headers);
    const params = mapRequestToAppropriateDialect(keyObject, options);
    params.host = rproxyHost;
    params.headers = requestlib.appendBStackHostHeader(hostname, params.headers || {});

    if (keyObject.translateLocalhostUrl) {
      let { requestDataUrlHostname, requestDataObj } = helper.getRequestObjAndUrlHostname(req_data, keyObject);

      if (/^\/wd\/hub\/session\/.*\/url$/.test(request.url) && request.method === 'POST' && helper.isLocalhostDomainOrIP(requestDataUrlHostname)) {
        params.body = params.body.replace(requestDataUrlHostname, 'bs-local.com');
        params.headers['content-length'] = params.body.length;

        try {
          const logDataObj = JSON.parse(request.log_data);
          logDataObj.url = requestDataObj.url + " (" + JSON.parse(params.body).url + ")";
          request.log_data = JSON.stringify(logDataObj);
        } catch (e) {
          HubLogger.miscLogger('JSONParseError', `Session Id: ${keyObject.rails_session_id} Error in updating text logs, stacktrace: ${e}`, LL.WARN);
        }
      }
    }

    try {
      if (/^\/wd\/hub\/session\/.*\/url$/.test(request.url) && request.method === 'POST' && !keyObject.userPassedLocalCap && !keyObject.nudgeLocalNotSetError) {
        let { requestDataUrlHostname } = helper.getRequestObjAndUrlHostname(req_data, keyObject);
        if (helper.isPrivateDomainOrIP(requestDataUrlHostname)) {
          keyObject.nudgeLocalNotSetError = requestDataUrlHostname;
          HubLogger.miscLogger('NudgeLocalNotSetError', `Session Id: ${keyObject.rails_session_id} Evaluating nudgeLocalNotSetError for hostname ${requestDataUrlHostname}`, LL.INFO);
        }
      }
    } catch (e) {
      HubLogger.miscLogger('NudgeLocalNotSetError', `Session Id: ${keyObject.rails_session_id} Error in evaluating nudgeLocalNotSetError, stacktrace: ${e}`, LL.WARN);
    }

    params.recordJarTime = true;
    params.mockPerformanceJarEndpoint = keyObject.mockPerformanceJarEndpoint;
    requestStateObj.params = params;
    return requestlib.callWithHook(params, 0, (longRequestOptions) => {
        HubLogger.miscLogger('longRequest', `Request to port ${options.port} took ${longRequestOptions.requestTime}`, LL.WARN);
        keyObject.udpKeys.transition_bucket = 'longRequest';
        pubSub.publish(constants.updateKeyObject, {
        session: keyObject.rails_session_id,
        changed: {
          udpKeys: keyObject.udpKeys,
        },
      });
    });
  })
  .then(async (output) => {
    helper.addToJarTime(keyObject.rails_session_id, output);

    if(longPageLoadTimeout) {
      clearTimeout(longPageLoadTimeout);
    }
    if(requestStateObj.safariPopupCheckTimeout) {
      clearTimeout(requestStateObj.safariPopupCheckTimeout);
      requestStateObj.hash = 'POST:url';
    }
    if(typeof output == 'undefined') return;

    if(typeof constants.global_registry[keyObject.rails_session_id] == 'undefined'){
      HubLogger.exceptionLogger('Error in response on createBridge: sessionNotFound, keyObject ' + HubLogger.logKeyObject(keyObject) + ' for url ' + request.url + ' request data ' + (requestStateObj.req_data ? requestStateObj.req_data.length : 0));
      HubLogger.miscLogger('createBridgeClientAndNode', '[SESSION_NOT_FOUND] Session not started or terminated for ' + keyObject.rails_session_id + ' in Response from JAR - Console Stack Trace ' + helper.getConsoleStackTrace(new Error().stack), LL.ERROR);
      return sessionNotFound(response, keyObject, 'Session was Deleted before Selenium Sent 200 Response');
    }

    if (keyObject.aiEnabledSessions && keyObject.realMobile && isNotUndefined(output) && isNotUndefined(output.headers) && isNotUndefined(output.headers.healingenabled)) {
      if (!isTrueString(keyObject.selfHealingSuccess) && output.headers.healingenabled.toString() === 'true'){
        keyObject.selfHealingSuccess = AICommandHelper.PUBLISH_AI_SUCCESS;
      }
      keyObject.aiHealingCommand = true; // Used for checking in raw logs
    } else if (keyObject.aiEnabledSessions && ((request.method === 'POST' && (request.url.match(/session\/[^/]+\/element$/) || request.url.match(/session\/[^/]+\/shadow\/[^/]+\/element$/) || request.url.match(/session\/[^/]+\/element\/[^/]+\/element$/) )) || (request.method === 'GET' && request.url.match(/session\/[^/]+\/element\/[^/]+\/shadow$/)) && !keyObject.appTesting)) {
      const handler = new AICommandHandler(keyObject, request, response);

      if (constants.global_registry && isNotUndefined(constants.global_registry[keyObject.rails_session_id]) && constants.execution_time_registry && isNotUndefined(constants.execution_time_registry[keyObject.rails_session_id])) {
        constants.execution_time_registry[keyObject.rails_session_id]['aiStartTime'] = Date.now();
        constants.global_registry[keyObject.rails_session_id]['automate_ai_find_element_count'] += 1;
      } else {
        HubLogger.exceptionLogger('Error in updating AI performance data: ' + HubLogger.logKeyObject(keyObject) + ' for url ' + request.url);
      }

      const respData = await handler.processResponse(output, requestStateObj.params);
      if (isNotUndefined(constants.global_registry[keyObject.rails_session_id].ai_healing_details) && helper.validateAIHealingDetails(constants.global_registry[keyObject.rails_session_id].ai_healing_details)) {
        HubLogger.newCGLogger('AI_HEALING_DETAILS', `Publishing ai_healing_details for session: ${keyObject.rails_session_id}`, LL.DEBUG);
        pubSub.publish(constants.updateKeyObject, {
          session: keyObject.rails_session_id,
          changed: {
            ai_healing_details: constants.global_registry[keyObject.rails_session_id].ai_healing_details,
          },
        });
      } else {
        HubLogger.newCGLogger('AI_HEALING_DETAILS', `Invalid ai_healing_details structure for session: ${keyObject.rails_session_id}`, LL.ERROR);
      }

      let status = 0, aiRetryCount = 0;
      if (respData.modifyResponse.toString() === 'true' && isNotUndefined(respData.modifyData)) {
        status = 1;
        aiRetryCount = respData.aiRetryCount || 1;
      }
      if (isNotUndefined(respData.aiFailureEvent) && respData.aiFailureEvent.toString() === 'true') {
        status = 404;
        aiRetryCount = respData.aiRetryCount;
      }

      helper.stopSeleniumClockAI(keyObject.rails_session_id, request.url, request.method, helper.isW3C(keyObject), status, aiRetryCount);

      if(respData.modifyResponse.toString() === 'true' && isNotUndefined(respData.modifyData)) {
        const newData = respData.modifyData;
        Object.keys(newData).forEach((key) => {
          output[key] = newData[key];
        });
      }
    }

    if(helper.isAppleOs(keyObject.os)) {
      output.data = redactor.redactSensitiveDataFromSessionInfo(output.data);
    }

    var data = output.data;
    hash = requestStateObj.hash;
    if(constants.timeout_registry[keyObject.rails_session_id]) {
      keyObject.timestamp = Date.now();
    }

    if (attempt >= 2 && requestStateObj.hash == 'POST:url-https-auth') {
      requestStateObj.hash = 'POST:url';
    }

    // Add to this hash to run custom script after open url and return
    var requestScriptHash = {
      'POST:basic-auth-ios': {
        'name': 'BasicAuthSafari',
        'script': 'PhishingAlertController.ignoreWarningSelected();',
      },
      'POST:safari-11-certs': {
        'name': 'AcceptSslSafari11',
        'script': 'if(document.title === \'This Connection Is Not Private\') { CertificateWarningController.visitInsecureWebsiteWithTemporaryBypass(); }',
      },
    };
    let pathForSrcipt = `/wd/hub/session/${requestStateObj.remoteSessionID}/execute`;
    if(keyObject.dialect === 'W3C') {
      pathForSrcipt = `${pathForSrcipt}/sync`;
    }
    if(requestScriptHash[requestStateObj.hash] && output.statusCode === 200) {
      var currentCase = requestScriptHash[requestStateObj.hash];
      return helper.logAppiumBridgeCommand(keyObject, currentCase['name'], 'start')
      .then(() => Promise.delay(500))
      .then(function(){
        let headers = {
          'accept': 'application/json',
          'content-type': 'application/json; charset=utf-8',
          'accept-encoding': 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
        };
        headers = helper.updateHeadersForSelenium4Jars(keyObject, headers);
        requestlib.appendBStackHostHeader(hostname, headers);
        let options = {
          method: 'POST',
          hostname: rproxyHost,
          port: keyObject.port,
          path: pathForSrcipt,
          body: Buffer.from(JSON.stringify({
            'script': currentCase['script'],
            'args': [],
          })),
          headers,
        };
        const params = mapRequestToAppropriateDialect(keyObject, options);
        requestlib.call(params)
        .then((res) => {
          requestStateObj.hash = 'POST:url';
          requestStateObj.output = {statusCode: 200, headers: constants.CHUNKED_HEADER};
          requestStateObj.data = '{"state":"success","sessionId":"'+clientSessionID+'","value":null,"class":"org.openqa.selenium.remote.Response","status":0}';
          HubLogger.miscLogger(currentCase['name'] + ': returned', res.data, LL.DEBUG, keyObject.debugSession);
          var actionAfterPageLoad = function() {
            helper.logAppiumBridgeCommand(keyObject, currentCase['name'], 'stop')
            .then(function(){
              bridge.sendResponse(keyObject, requestStateObj);
            });
          };
          Promise.delay(constants.BASIC_AUTH_IOS_TIMEOUT).then(() => {
             supporting.waitForPageLoad(actionAfterPageLoad, actionAfterPageLoad, keyObject, constants.BASIC_AUTH_IOS_RESPONSE_TIMEOUT);
          });
        }).catch((err) => {
          requestStateObj.hash = 'POST:url';
          HubLogger.miscLogger('Error during ' + currentCase['name'], err.toString(), LL.ERROR);
          helper.logAppiumBridgeCommand(keyObject, currentCase['name'], 'stop')
          .then(function(){
            bridge.sendErrorResponse(keyObject, requestStateObj);
          });
        });
      });
    }

    var actionAfterPageLoadBasicAuth = function() {
      requestStateObj.hash = 'POST:url';
      requestStateObj.output = {statusCode: 200, headers: constants.CHUNKED_HEADER};
      requestStateObj.data = '{"state":"success","sessionId":"'+clientSessionID+'","value":null,"class":"org.openqa.selenium.remote.Response","status":0}';
      helper.logAppiumBridgeCommand(keyObject, 'iOSBasicAuthHandler', 'stop')
      .then(function(){
        HubLogger.miscLogger('iOSBasicAuthHandler', `Stopped Basic Auth session: ${keyObject.rails_session_id}  Device: ${keyObject.deviceName}`, LL.DEBUG, keyObject.debugSession);
        bridge.sendResponse(keyObject, requestStateObj);
      });
    };

    var successCallbackAcceptSslCert = function () {
      requestStateObj.hash = 'POST:url';
      requestStateObj.output = {statusCode: 200, headers: constants.CHUNKED_HEADER};
      HubLogger.miscLogger('iOSAcceptSSLCertHandler', `started  for session: ${keyObject.rails_session_id}  Device: ${keyObject.deviceName}`, LL.DEBUG, keyObject.debugSession);
      requestStateObj.data = '{"state":"success","sessionId":"' + clientSessionID + '","value":null,"class":"org.openqa.selenium.remote.Response","status":0}';
      helper.logAppiumBridgeCommand(keyObject, 'iOSAcceptSSLCertHandler', 'stop')
        .then(function(){
          HubLogger.miscLogger('iOSAcceptSSLCertHandler', `stopped  for session: ${keyObject.rails_session_id}  Device: ${keyObject.deviceName}`, LL.DEBUG, keyObject.debugSession);
          if (requestStateObj.hash === 'POST:url-https-ios-gte-11') {
            // Sends response back in case of no basic auth after ssl cert
            bridge.sendResponse(keyObject, requestStateObj);
          } else {
            // Calling basic auth after ssl cert
            var username, password;
            try {
              var parsed = urlModule.parse(JSON.parse(requestStateObj.req_data)['url']);
              username = parsed.auth.split(':')[0];
              password = parsed.auth.split(':')[1];
            } catch (e) {
              return actionAfterPageLoadBasicAuth();
            }
            HubLogger.miscLogger('iOSBasicAuthHandlerAfterSSLCert', `Started Basic Auth session: ${keyObject.rails_session_id}  Device: ${keyObject.deviceName}`, LL.DEBUG, keyObject.debugSession);
            return helper.logAppiumBridgeCommand(keyObject, 'iOSBasicAuthHandler', 'start')
            .then(function() {
              return bridge.iOSBasicAuthHandler(keyObject, username, password, actionAfterPageLoadBasicAuth);
            });
          }
        });
    };

    if (requestStateObj.hash === 'POST:url-https-ios-gte-11' || requestStateObj.hash === 'POST:url-https-basic-auth-ios-gte-11') {
      return helper.logAppiumBridgeCommand(keyObject, 'iOSAcceptSSLCertHandler', 'start')
      .then(function() {
        return iOSAcceptSSLCertHandler(keyObject, successCallbackAcceptSslCert);
      });
    }


    if (['POST:basic-auth-ios-gte11'].includes(requestStateObj.hash)) {
      var username, password;
      try {
        var parsed = urlModule.parse(JSON.parse(requestStateObj.req_data)['url']);
        username = parsed.auth.split(':')[0];
        password = parsed.auth.split(':')[1];
      } catch (e) {
        return actionAfterPageLoadBasicAuth();
      }
      HubLogger.miscLogger('iOSBasicAuthHandler', `Started Basic Auth session: ${keyObject.rails_session_id}  Device: ${keyObject.deviceName}`, LL.DEBUG, keyObject.debugSession);
      return helper.logAppiumBridgeCommand(keyObject, 'iOSBasicAuthHandler', 'start')
      .then(function() {
        return bridge.iOSBasicAuthHandler(keyObject, username, password, actionAfterPageLoadBasicAuth);
      });
    }

    if (requestStateObj.hash === 'POST:url-inject-ios') {
      var successCallbackPostInject = function () {
        requestStateObj.hash = 'POST:url';
        requestStateObj.output = {statusCode: 200, headers: constants.CHUNKED_HEADER};
        requestStateObj.data = '{"state":"success","sessionId":"' + clientSessionID + '","value":null,"class":"org.openqa.selenium.remote.Response","status":0}';
        helper.logAppiumBridgeCommand(keyObject, 'iOSInjectAllowPopupScript', 'stop')
        .then(function(){
          bridge.sendResponse(keyObject, requestStateObj);
        });
      };

      return helper.logAppiumBridgeCommand(keyObject, 'iOSInjectAllowPopupScript', 'start')
      .then(function() {
        return bridge.iOSInjectAllowPopupScript(keyObject, successCallbackPostInject);
      });
    }

    var log_data = (requestStateObj.req_data && requestStateObj.req_data != '') ? JSON.parse(requestStateObj.req_data) : {};
    if(log_data && log_data.url) {
      log_data.url = log_data.url.toString();
    }

    if (keyObject.deviceName && keyObject.deviceName.match(/iphone|ipad/i) && log_data && log_data.url && log_data.url.startsWith('https:') && keyObject.certs && log_data.url.indexOf('@') > -1 && attempt < 2 ){
      request.lastRequestLogicDone = true;
      return createBridgeClientAndNode(keyObject, request, response, callbacks, attempt+1, index_counter, req_data_retry);
    }

    var parsed_data;
    if (data) {
      try {
        parsed_data = JSON.parse(data);
      } catch(e) {
        HubLogger.miscLogger('JSON-PARSE', 'Error parsing:' + data, LL.ERROR);
        HubLogger.seleniumStats('automate-json-data-error', keyObject, e.toString(), 'Node data did not result in a valid json:\n' + data, keyObject.key, 'nodeError');
      }
    }

    var changed = { timestamp: keyObject.timestamp };
    var executeClick = hash == 'POST:execute' && requestStateObj && requestStateObj.req_data && requestStateObj.req_data.match(/\.click\(\)/) !== null;
    // temporary instrumentation to instrument postive executeClick cases
    if (constants.INSTRUMENT_EXECUTE_CLICK && executeClick) {
      try {
        let session_id;
        let user_id;
        if (keyObject.rails_session_id) {
          session_id = keyObject.rails_session_id;
          if (constants.global_registry[keyObject.rails_session_id] && constants.global_registry[keyObject.rails_session_id].user_id) {
            user_id = constants.global_registry[keyObject.rails_session_id].user_id;
          }
        }
        helper.PingZombie({
          kind: 'execute-click-positive',
          user_id,
          data: { scriptLength: requestStateObj.req_data.length },
          region: constants.region,
          machine: constants.osHostName,
          session_id
        });
      } catch(e) {
        /* istanbul ignore next line */
        HubLogger.miscLogger('INSTRUMENT_EXECUTE_CLICK_ERROR', 'Some error occurred while instrumenting execute click. Exception: ' + (e ? e.toString() :''), LL.ERROR);
      }
    }
    var loadingHash = hash == 'POST:url' || hash == 'POST:click' || hash == 'POST:refresh' || hash == 'POST:submit' || hash === 'DELETE:window' || executeClick;
    request.loadingHash = loadingHash;

    keyObject.lastRequest = hash;
    changed['lastRequest'] = hash;

    if(loadingHash){
      changed['instable'] = loadingHash;
    }

    if(request && request.method === 'POST' && request.url.indexOf('/url') > -1 && keyObject.browser == 'safari' && helper.didReachPrivoxyTimeoutForSafari(request.log_date, helper.getDate())) {
      keyObject.safariPrivoxyTimeout = true;
      changed['safariPrivoxyTimeout'] = true;
    }
    pubSub.publish(constants.updateKeyObject, {
      session: keyObject.rails_session_id,
      changed: changed,
    });

    //Detect Page Load timeout and instrument
    if(parsed_data && constants.global_registry[keyObject.rails_session_id] && !keyObject.appTesting && isPageLoadTimeoutErrorType(parsed_data)){
      HubLogger.miscLogger('setPageLoadTimeoutOnTerminal', 'sessionId: ' + keyObject.rails_session_id + ' status: page load timeout caught, Status: ' + parsed_data.status + ' State: ' + parsed_data.state, LL.DEBUG, keyObject.debugSession) ;
      const kind = 'automate-page-load-timeout';
      const errorMsg = (parsed_data.value && parsed_data.value.error && typeof parsed_data.value.error == 'string') ? parsed_data.value.error.substring(0,50) : ((parsed_data.error && typeof parsed_data.error == 'string') ? parsed_data.error.substring(0,50) : 'Not able to parse error data');
      const msg = (parsed_data.message && typeof parsed_data.message == 'string') ? parsed_data.message.substring(0,50) : ((parsed_data.value && parsed_data.value.message && typeof parsed_data.value.message == 'string') ? parsed_data.value.message.substring(0,50) : 'Not able to parse data');
      helper.PingZombie({
        kind,
        browser: constants.global_registry[keyObject.rails_session_id].browser,
        os: constants.global_registry[keyObject.rails_session_id].os,
        browser_version: constants.global_registry[keyObject.rails_session_id].browser_version,
        user_id: constants.global_registry[keyObject.rails_session_id].user_id,
        device: constants.global_registry[keyObject.rails_session_id].device,
        error: errorMsg,
        data: {status: parsed_data.status, state: parsed_data.state, selenium_version: keyObject.selenium_version, message: msg,lastOpenedUrl: constants.global_registry[keyObject.rails_session_id].lastOpenedUrl},
        region: constants.region,
        machine: constants.osHostName,
        session_id: constants.global_registry[keyObject.rails_session_id].rails_session_id
      });
      if (!constants.global_registry[keyObject.rails_session_id].pageLoadError){
        constants.global_registry[keyObject.rails_session_id].pageLoadError = constants.PAGE_LOAD_ERROR_DETECTED;
      }
    }
    //Commenting out as skipping retries for pageload timeouts.
    //TODO: Check and remove this stale blocks after the code getting stable or uncomment and bring back if retries are neeeded.
    /*
    if(parsed_data && constants.global_registry[keyObject.rails_session_id] && isPageLoadTimeoutError(parsed_data)){
      // parsed_data.status = 0;
      // delete parsed_data.value;
      // data = JSON.stringify(parsed_data);
      // output.statusCode = 200;
      if(attempt == 1 && request.method === 'POST' && request.url.indexOf('/url') > -1 && keyObject.browser == 'chrome'){
        HubLogger.miscLogger('setPageLoadTimeoutOnTerminal', 'sessionId: ' + keyObject.rails_session_id + ' status: page load timeout caught, retrying. Status: ' + parsed_data.status + ' State: ' + parsed_data.state, LL.DEBUG, keyObject.debugSession) ;
        if(parsed_data && isPageLoadTimeoutError(parsed_data) && keyObject.browser == 'chrome' && request.url.indexOf('/url') > -1 && keyObject.setPageLoadTimeout) {
          supporting.setPageLoadTimeoutOnTerminal(keyObject, function() {
            createBridgeClientAndNode(keyObject, request, response, callbacks, ++attempt, index_counter, req_data_retry);
          }, 200000);
        }
        return;
      }
      else if(attempt == 1 && request.method === 'POST' && (request.url.indexOf('/url') > -1 || request.url.indexOf('/submit') > -1) && keyObject.browser == 'firefox'){
        HubLogger.miscLogger('setPageLoadTimeoutOnTerminal', 'sessionId: ' + keyObject.rails_session_id + ' status: page load timeout caught, resetting to about:blank and retrying Status: ' + parsed_data.status + ' State: ' + parsed_data.state, LL.DEBUG, keyObject.debugSession) ;
        var obj = {
          data: data,
          output: output,
          remoteSessionID: remoteSessionID,
          clientSessionID: clientSessionID,
          index_counter: index_counter,
          callbacks: callbacks,
          hostname: hostname,
          originalUrl: originalUrl,
        };

        var cb = function(){
          if(request.url.indexOf('/url')){
            return createBridgeClientAndNode(keyObject, request, response, callbacks, ++attempt, index_counter, req_data_retry);
          } else {
            return processResponse(request, response, keyObject, obj);
          }
        };

        return supporting.openURLOnTimeout(keyObject, cb, cb);
      }
      else {
        HubLogger.miscLogger('setPageLoadTimeoutOnTerminal', 'sessionId: ' + keyObject.rails_session_id + ' status: page load timeout caught, moving forward. Status: ' + parsed_data.status + ' State: ' + parsed_data.state, LL.DEBUG, keyObject.debugSession) ;
        if( attempt > 1 && ( keyObject.browser === 'chrome' || keyObject.browser === 'firefox' ) && parsed_data.status === 28 && parsed_data.value && JSON.stringify(parsed_data.value).match(/timeout: cannot determine loading status|timeout: Timed out receiving message from renderer/i) ) {
          supporting.checkPageLoadOnTerminal(keyObject, function(data, output) {
            HubLogger.miscLogger('openURLLoadingStatusIgnore', 'Page Load is complete. session: ' + clientSessionID + ' output: ' + output, LL.DEBUG, keyObject.debugSession);
            requestStateObj.output = {statusCode: 200, headers: constants.CHUNKED_HEADER};
            requestStateObj.data = '{"state":"success","sessionId":"' + clientSessionID + '","value":null,"class":"org.openqa.selenium.remote.Response","status":0}';
            if(parsed_data && isPageLoadTimeoutError(parsed_data) && keyObject.browser == 'chrome' && request.url.indexOf('/url') > -1 && keyObject.setPageLoadTimeout) {
              supporting.setPageLoadTimeoutOnTerminal(keyObject, function() { bridge.sendResponse(keyObject, requestStateObj); }, 200000);
            } else {
              bridge.sendResponse(keyObject, requestStateObj);
            }
          }, function(err, output) {
            HubLogger.miscLogger('openURLLoadingStatusIgnore', 'Page Load is NOT complete. session: ' + clientSessionID, LL.DEBUG, keyObject.debugSession);
            requestStateObj.output = output;
            requestStateObj.data = data;
            bridge.sendResponse(keyObject, requestStateObj);
          });
          return;
        }
      }
    }
    */
    updateKeyObjectForUdpKeys(keyObject, parsed_data, hash, attempt);

    if(parsed_data && parsed_data.status == 13 && parsed_data.value && parsed_data.value.message) {
      if (parsed_data.value.message.match('Error communicating with the remote browser')) {
        if(keyObject.browser.match(/chrome|firefox|safari/i) && attempt == 1){
          keyObject.captureCrash = true;
          pubSub.publish(constants.updateKeyObject, {
            session: keyObject.rails_session_id,
            changed: {
              captureCrash: true,
            },
          });
          return ha.setData(keyObject.rails_session_id, keyObject, function() {
            if(request.method === 'POST' && request.url.indexOf('/url') > -1){
              requestStateObj.output = {statusCode: 200, headers: constants.CHUNKED_HEADER};
              requestStateObj.data = '{"state":"success","sessionId":"'+clientSessionID+'","value":null,"class":"org.openqa.selenium.remote.Response","status":0}';
              bridge.sendResponse(keyObject, requestStateObj);
              return;
            }
            else {
              HubLogger.miscLogger('BrowserDiedError', 'sessionId: ' + keyObject.rails_session_id + ' status: Error communicating with remote browser, retrying. Status: ' + parsed_data.status + ' State: ' + parsed_data.state, LL.ERROR) ;
              request.lastRequestLogicDone = true;
              return createBridgeClientAndNode(keyObject, request, response, callbacks, ++attempt, index_counter, req_data_retry);
            }
          });
        } else {
          keyObject.node_died = true;
        }
      } else if (parsed_data.value.message.match('java.net.ConnectException: (Operation timed out|Connection refused)') && attempt == 1) {
        HubLogger.miscLogger('ConnectException', 'sessionId: ' + keyObject.rails_session_id + ' status: java.net.ConnectException: ' + parsed_data.value.message + ', retrying. Status: ' + parsed_data.status + ' State: ' + parsed_data.state, LL.ERROR) ;
        return createBridgeClientAndNode(keyObject, request, response, callbacks, ++attempt, index_counter, req_data_retry);
      } else if (parsed_data.value.message.match('org.apache.http.conn.HttpHostConnectException') && attempt == 1) {
        // Adding this as this class is timeout between jar and geckodriver. Asana: https://app.asana.com/0/37727939018413/490597111505988
        HubLogger.miscLogger('ConnectException', 'sessionId: ' + keyObject.rails_session_id + ' status: org.apache.http.conn.HttpHostConnectException, retrying. Status: ' + parsed_data.status + ' State: ' + parsed_data.state, LL.INFO) ;
        return Promise.delay(constants.httpHostConnectExceptionRetryTimeout).then(() => createBridgeClientAndNode(keyObject, request, response, callbacks, ++attempt, index_counter, req_data_retry));
      } else if (parsed_data.value.message.match('no such window: window was already closed')) {
        keyObject.node_died = true;
      }
    }

    if(parsed_data && parsed_data.status == 13 && parsed_data.value && parsed_data.value.class && (parsed_data.value.class.match(/java.lang.NullPointerException/))) {
      if(keyObject.browser && keyObject.browser.match(/safari|chrome|firefox/i) && attempt == 1){
        HubLogger.miscLogger('SafariNullPointerException', 'sessionId: ' + keyObject.rails_session_id + ' status: Got Null pointer exception, retrying. Status: ' + parsed_data.status + ' State: ' + parsed_data.state, LL.ERROR) ;
        request.lastRequestLogicDone = true;
        setTimeout(function(){ createBridgeClientAndNode(keyObject, request, response, callbacks, ++attempt, index_counter, req_data_retry); }, 2000);
        return;
      }
    }

    requestStateObj.output = output;
    if (request.url.match(/\/screenshot$/)) {
      requestStateObj.parsed_data = parsed_data;
    }

    if (keyObject.debugSession) {
      HubLogger.miscLogger('DebugResponse', JSON.stringify(requestStateObj.output), LL.DEBUG, keyObject.debugSession);
    }

    if(keyObject.realMobile && parsed_data && parsed_data.value && parsed_data.value.message) {
      if (parsed_data.status == 13 || parsed_data.status == 32) {
        parsed_data.value.message = 'Appium error: ' + parsed_data.value.message;
      }
      parsed_data.value.message = (parsed_data.value.message || '' );
      parsed_data.value.message = parsed_data.value.message.toString().replace(/\/usr\/local\/.browserstack\//g, '');
      data = JSON.stringify(parsed_data);
      const responseStatusCode = requestStateObj.output && requestStateObj.output.statusCode ? requestStateObj.output.statusCode : 200;
      response.writeHead(responseStatusCode, {'content-type': 'application/json; charset=utf-8', 'Source-From': constants.NGINX_SOURCE_FROM_CODES.JAR});
      if (keyObject.debugSession) {
        HubLogger.miscLogger('DebugResponse', JSON.stringify(response), LL.DEBUG, keyObject.debugSession);
      }
      keyObject.lastResponseStatus = `${responseStatusCode}::${helper.nestedKeyValueGeneric(parsed_data, ['status'], 'NOT_AVAILABLE', isNotUndefined)}`;
      ha.setData(keyObject.rails_session_id, keyObject);
    }

    requestStateObj.data = data;

    if (attempt < 2 && (requestStateObj.hash == 'POST:url-https-auth' || requestStateObj.edgeBasicAuthCheckTimeout || fileUploadTimeout)) {
      requestStateObj.returned = true;
      return;
    }

    if(requestStateObj.hash == 'POST:firefox-acceptssl' && output.statusCode !== 200 && attempt < 2) {
      const handler = new FirefoxAcceptSslHandler(keyObject, request, response);
      handler.handleCommandResult(requestStateObj);
      return;
    }

    if(request.method === 'POST' && (request.url.indexOf('/url') > -1 || request.url.indexOf('/click') > -1 || request.url.indexOf('/refresh') > -1 || request.url.indexOf('/window') > -1) && keyObject.browser == 'internet explorer' && parsed_data && parsed_data.status == 28){
      requestStateObj.data = '{"state":"success","sessionId":"' + clientSessionID + '","value":null,"class":"org.openqa.selenium.remote.Response","status":0}';
      return bridge.sendResponse(keyObject, requestStateObj);
    }

    if(request.method === 'POST' && (request.url.indexOf('/url') > -1 || request.url.indexOf('/click') > -1 || request.url.indexOf('/refresh') > -1 || request.url.indexOf('/window') > -1) && keyObject.browser == 'internet explorer' && parseInt(keyObject.browser_version) == 6)
      return Promise.delay(5000).then(() => bridge.sendResponse(keyObject, requestStateObj));

    if(request.method === 'POST' && request.url.indexOf('/url') > -1 && request.logging_data){
      request.logging_data.end_time = (new Date()).getTime();
      request.logging_data.open_url_time = request.logging_data.end_time - request.logging_data.start_time;
    }

    if(parsed_data && isPageLoadTimeoutError(parsed_data) && keyObject.browser == 'chrome' && request.url.indexOf('/url') > -1 && keyObject.setPageLoadTimeout) {
      supporting.setPageLoadTimeoutOnTerminal(keyObject, function() { bridge.sendResponse(keyObject, requestStateObj); }, 200000);
    } else {
      bridge.sendResponse(keyObject, requestStateObj);
    }
  })
  .catch((e) => {
    if(longPageLoadTimeout) {
      clearTimeout(longPageLoadTimeout);
    }
    if(typeof constants.global_registry[keyObject.rails_session_id] == 'undefined'){
      HubLogger.exceptionLogger('Error in response on createBridge: sessionNotFound, keyObject ' + HubLogger.logKeyObject(keyObject) + ' for url ' + request.url + ' request data ' + (requestStateObj.req_data ? requestStateObj.req_data.length : 0));
      HubLogger.miscLogger('SESSION_NOT_FOUND', 'Session not started or terminated for ' + keyObject.rails_session_id + ' in Response for JAR exception. Exception: ' + (e ? e.toString() :''), LL.WARN);
      return sessionNotFound(response, keyObject, 'Session was Deleted Before Selenium Sent Non 200 Response');
    }
    switch (e.type) {
      case 'ResponseError':
        HubLogger.exceptionLogger('Error in response on createBridge, sessionId: ' + keyObject.rails_session_id + ' error ' + e + '\n keyObject ' + HubLogger.logKeyObject(keyObject));
        break;
      case 'RequestError':
        HubLogger.exceptionLogger('Error in request on createBridge , sessionId: ' + keyObject.rails_session_id + ' error ' + e + '\n keyObject ' + HubLogger.logKeyObject(keyObject));
        bridge.requestError(e, keyObject, requestStateObj);
        break;
      case 'TimeoutError':
        HubLogger.exceptionLogger('Timeout error on createBridge , sessionId: ' + keyObject.rails_session_id + ' error ' + e + '\n keyObject ' + HubLogger.logKeyObject(keyObject));
        requestStateObj.attempt = 3;
        bridge.checkDebugLogging(request, response, keyObject, function(){
          bridge.requestError(e, keyObject, requestStateObj);
        });
        break;
      case constants.customErrorTypes.userRequestNotJSON:
        bridge.requestError(e, keyObject, requestStateObj);
        break;

      default: {
        HubLogger.exceptionLogger('CHECK createBridgeClientAndNode Exception ' + e.stack.toString());
        let dataToSend = JSON.stringify({value: {message: ''}, sessionId: '', 'status': 13});
        if (!((keyObject || {}).appTesting)) {
          HubLogger.instrumentationStats('Exception createBridgeClientAndNode', keyObject, '', dataToSend);
        }
        helper.respondWithError(request, response, dataToSend);
        throw e;
      }
    }
  });
  });
}
exports.createBridgeClientAndNode = createBridgeClientAndNode;

var isPageLoadTimeoutErrorType = function(parsed_data){
  if(parsed_data.status === 28 || parsed_data.status === 21){
    return true;
  } else if(parsed_data.value && JSON.stringify(parsed_data.value).match(constants.PAGE_LOAD_ERROR_REGEX)){
    return true;
  } else if(parsed_data.message && JSON.stringify(parsed_data.message).match(constants.PAGE_LOAD_ERROR_REGEX)){
    return true;
  }
  return false;
};
exports.isPageLoadTimeoutErrorType = isPageLoadTimeoutErrorType;

function isPageLoadTimeoutBrowser(browser){
  return constants.INTERNAL_PAGELOAD_TIMEOUT_BROWSERS.indexOf(browser) > -1;
}
exports.isPageLoadTimeoutBrowser = isPageLoadTimeoutBrowser;

function typesafeObject(obj) {
  for (var key in obj) {
    if (typeof obj[key] == 'object')
      obj[key] = typesafeObject(obj[key]);
    else if (typeof obj[key] == 'string') {
      if (obj[key].toLowerCase() == 'false')
        obj[key] = false;
      else if (obj[key].toLowerCase() == 'true')
        obj[key] = true;
    }
  }
  return obj;
}


function truncateCap(caps, capPath, charLimit, replaceWith) {
  if (!(caps && capPath)) {
    return;
  }
  var capValue = helper.nestedKeyValue(caps, capPath);
  if (typeof capValue !== 'string') {
    return;
  }
  if (capValue.length > charLimit) {
    capValue = capValue.substring(0, charLimit) + replaceWith;
  }
  return capValue;
}

function restoreHosts(caps, options){
  let truncatedHosts = undefined;
  if (caps && caps.desiredCapabilities && caps.desiredCapabilities['browserstack.hosts']){
    truncatedHosts = caps.desiredCapabilities['browserstack.hosts'];
  }

  if (options && options.browserstackParams && options.browserstackParams['browserstack.hosts']){
    caps.desiredCapabilities['browserstack.hosts'] = options.browserstackParams['browserstack.hosts'];
  }
  return truncatedHosts;
}

function truncateHosts(caps, rails_omitted_caps){
    var truncatedHosts = truncateCap(caps, ['desiredCapabilities', 'browserstack.hosts'], 25, '...');
    addBSKeyToOmittedCaps(rails_omitted_caps, caps, 'hosts');
    if (truncatedHosts) {
      caps['desiredCapabilities']['browserstack.hosts'] = truncatedHosts;
    }
}

exports.truncateHosts = truncateHosts;
exports.restoreHosts = restoreHosts;

function addBSKeyToOmittedCaps(rails_omitted_caps, caps, bsKey) {
  if (!(rails_omitted_caps && caps && bsKey)) {
    return;
  }
  const capName = `browserstack.${bsKey}`;
  if (helper.nestedKeyValue(caps, ['desiredCapabilities', capName])) {
    rails_omitted_caps[capName] = caps['desiredCapabilities'][capName];
    delete caps['desiredCapabilities'][capName];
  }
  if (helper.nestedKeyValue(caps, ['desiredCapabilities', 'bstack:options', bsKey])) {
    rails_omitted_caps[capName] = caps['desiredCapabilities']['bstack:options'][bsKey];
    delete caps['desiredCapabilities']['bstack:options'][bsKey];
  }
  if (helper.nestedKeyValue(caps, ['capabilities', 'alwaysMatch', 'bstack:options', bsKey])) {
    rails_omitted_caps[capName] = caps['capabilities']['alwaysMatch']['bstack:options'][bsKey];
    delete caps['capabilities']['alwaysMatch']['bstack:options'][bsKey];
  }
  let firstMatch = helper.nestedKeyValue(caps, ['capabilities', 'firstMatch']);
  if (firstMatch && firstMatch.length > 0 && firstMatch[0]['bstack:options'] && firstMatch[0]['bstack:options'][bsKey]) {
    rails_omitted_caps[capName] = caps['capabilities']['firstMatch'][0]['bstack:options'][bsKey];
    delete caps['capabilities']['firstMatch'][0]['bstack:options'][bsKey];
  }
}


function add_to_omitted_caps(rails_omitted_caps, caps, optionsKey, subKey, omittedKey) {
  if(caps[optionsKey] && caps[optionsKey][subKey]){
    rails_omitted_caps[omittedKey] = caps[optionsKey][subKey];
    delete caps[optionsKey][subKey];
  }
}

function add_to_redacted_caps(rails_omitted_caps, caps, optionsKey, subKey, omittedKey) {
  if(caps[optionsKey] && caps[optionsKey][subKey] && caps[optionsKey][subKey] != "[REDACTED]"){
    rails_omitted_caps[omittedKey] = caps[optionsKey][subKey];
    caps[optionsKey][subKey] = "[REDACTED]";
  }
}

function deleteRailsCapsWithCharLimit(caps, currentDepth) {
  try {
    // Return if the maxDepth has been reached
    if ( currentDepth >= constants.railsDeleteCapsMethodMaxDepth ) {
      helper.PingZombie({
        'category': 'selenium-stats',
        'kind' : 'delete-rails-caps-depth-limit-reached',
        'timestamp': (Math.round(new Date().getTime() / 1000).toString()),
        'region': constants.region,
        'machine': constants.osHostName
      });
      return caps;
    }
    // For Numbers, check and convert to string for length checks on numbers.
    if (typeof caps === 'number') {
      if (caps.toString().length > constants.railsPerCapMaxLength) {
        return undefined;
      }
      return caps;
    } else if (typeof caps === 'string') {
      // Check and trim string length
      if (caps.length > constants.railsPerCapMaxLength) {
        return undefined;
      }
      return caps;
    } else if (Array.isArray(caps)) {
      // Remove duplicates from array
      const uniqueArray = [...new Set(caps)];

      // Check total length of array as a string
      const arrayLength = JSON.stringify(uniqueArray).length;

      // If total length exceeds maxLength, return undefined
      if (arrayLength > constants.railsPerCapMaxLength) {
        return undefined;
      }

      // Otherwise, check the elements in the unique array and return it
      let resultArray = uniqueArray.map((element) => deleteRailsCapsWithCharLimit(element, currentDepth+1)).filter(Boolean);
      return resultArray;
    } else if (typeof caps === 'object' && caps !== null) {
      // Recursively process each property in the object
      const processedObject = {};
      for (const key in caps) {
        let processedValue = undefined;
        // In case of firstMatch, we have only one element in the array with an object of caps. If extension is sent and exceeds size, entire firstMatch will be deleted.
        // So, will send that object for sanitisation and store it as array and save it below to retain the firstMatch caps with retaining all caps within limits.
        if (key === 'firstMatch' && Array.isArray(caps[key])) {
          processedValue = [deleteRailsCapsWithCharLimit(caps[key][0], currentDepth+1)];
        } else {
          processedValue = deleteRailsCapsWithCharLimit(caps[key], currentDepth+1);
        }
        // Check if the processed value is not undefined before adding to the result
        if (processedValue !== undefined) {
          processedObject[key] = processedValue;
        }
      }
      return processedObject;
    }
  } catch(e) {
    HubLogger.exceptionLogger('Error in sanitising caps with char limit : ' + e.stack.toString(), '-', '-', '-', '-', '-DELETERAILSCAPSWITHCHARLIMIT');
    helper.PingZombie({
      kind: 'rails-caps-char-limit-check-exception',
      data: e.stack.toString(),
      region: constants.region,
      machine: constants.osHostName,
    });
  }
  // This will also return caps as such if the data type doesn't match any of them or if there is any exceptions caught above.
  return caps;
}
exports.deleteRailsCapsWithCharLimit = deleteRailsCapsWithCharLimit;

function delete_rails_caps(desiredCapsString, desiredCaps) {
  try{
    if (desiredCapsString && desiredCapsString.length > constants.railsDeleteLength) {
      for (const option of constants.BROWSER_OPTIONS.concat(constants.railsDeleteKeys)) {
        helper.PingZombie({
          kind: 'deleting-rails-caps',
          data: `length - ${desiredCapsString.length}`,
          region: constants.region,
          machine: constants.osHostName,
        });
        if(desiredCaps&& desiredCaps[option]) {
          delete desiredCaps[option];
        }
        if(desiredCaps && desiredCaps['bstack:options'] && desiredCaps['bstack:options'][option]) {
          delete desiredCaps['bstack:options'][option];
        }
        if(desiredCaps && desiredCaps['W3C_capabilities'] && desiredCaps['W3C_capabilities']['alwaysMatch'] && desiredCaps['W3C_capabilities']['alwaysMatch'][option]) {
          delete desiredCaps['W3C_capabilities']['alwaysMatch'][option];
        }
        if(desiredCaps && desiredCaps['W3C_capabilities'] && desiredCaps['W3C_capabilities']['alwaysMatch'] && desiredCaps['W3C_capabilities']['alwaysMatch']['bstack:options'] && desiredCaps['W3C_capabilities']['alwaysMatch']['bstack:options'][option]) {
          delete desiredCaps['W3C_capabilities']['alwaysMatch']['bstack:options'][option];
        }
        if(desiredCaps && desiredCaps['W3C_capabilities'] && desiredCaps['W3C_capabilities']['firstMatch'] && desiredCaps['W3C_capabilities']['firstMatch'][0] && desiredCaps['W3C_capabilities']['firstMatch'][0][option]) {
          delete desiredCaps['W3C_capabilities']['firstMatch'][0][option];
        }
        if(desiredCaps && desiredCaps['W3C_capabilities'] && desiredCaps['W3C_capabilities']['firstMatch'] && desiredCaps['W3C_capabilities']['firstMatch'][0] && desiredCaps['W3C_capabilities']['firstMatch'][0]['bstack:options'] && desiredCaps['W3C_capabilities']['firstMatch'][0]['bstack:options'][option]) {
          delete desiredCaps['W3C_capabilities']['firstMatch'][0]['bstack:options'][option];
        }
      }
      desiredCapsString = JSON.stringify(desiredCaps);
    }
  } catch (e) {
    HubLogger.exceptionLogger('Error : '  + e.stack.toString(), '-', '-', '-', '-', '-DELETERAILS');
  }
  return desiredCapsString;
}

exports.delete_rails_caps = delete_rails_caps;

function sanitiseRailsCapsLimit(capsString, caps, instrumentationData = {}) {
  try {
    if ( capsString && capsString.length <= constants.railsDeleteLength ) {
      return capsString;
    }
    const sanitisedCaps = deleteRailsCapsWithCharLimit(caps, 0);
    const sanitisedCapsString = JSON.stringify(sanitisedCaps);
    if ( caps && sanitisedCaps && capsString !== sanitisedCapsString ) {
      helper.PingZombie({
        kind: 'rails-caps-modified-in-hub',
        data: { ...instrumentationData, length_before_change: capsString.length, length_after_change: sanitisedCapsString.length },
        region: constants.region,
        machine: constants.osHostName,
      });
    }
    return delete_rails_caps(sanitisedCapsString, sanitisedCaps);
  } catch (e) {
    HubLogger.exceptionLogger('Error in sanitiseRailsCapsLimit: '  + e.stack.toString(), '-', '-', '-', '-', '-DELETERAILS');
    helper.PingZombie({ kind: 'sanitise-rails-caps-limit-exception', data: { ...instrumentationData, error: e.toString() }, region: constants.region, machine: constants.osHostName });
  }
  return capsString;
}

exports.sanitiseRailsCapsLimit = sanitiseRailsCapsLimit;

function updateCdpCapabilities(options, k, wsProxyHost) {
  if (isNotUndefined(helper.nestedKeyValue(k, ['value', 'capabilities', 'se:cdp']))) {
    if (helper.validateCdpSeleniumJar(options.browserstackParams["browserstack.selenium.jar.version"]) && isTrueString(options.browserstackParams['browserstack.seleniumCdp'])) {
      options.seCdpUpstream = k.value.capabilities['se:cdp'].replace(options.port, REMOTE_DEBUGGER_PORT).replace(options.host_name, options.rproxyHost);
      k.value.capabilities['se:cdp'] = `${constants.WS_PROXY_SCHEME}://${wsProxyHost}:${constants.WS_PROXY_PORT}/session/${options.sessionId}/se/cdp`;
    } else {
      delete k.value.capabilities['se:cdp'];
    }
  }
  return { options, k };
}

exports.updateCdpCapabilities = updateCdpCapabilities;

function updateBidiCapabilities(options, k, wsProxyHost) {
  if (isNotUndefined(helper.nestedKeyValue(k, ['value', 'capabilities', 'webSocketUrl']))) {
    if (helper.validateBidiSeleniumJar(options.browserstackParams["browserstack.selenium.jar.version"]) && isTrueString(options.browserstackParams['browserstack.seleniumBidi'])) {
      options.seBidiUpstream = k.value.capabilities.webSocketUrl.replace(options.port, REMOTE_DEBUGGER_PORT).replace(options.host_name, options.rproxyHost);
      k.value.capabilities.webSocketUrl = `${constants.WS_PROXY_SCHEME}://${wsProxyHost}:${constants.WS_PROXY_PORT}/session/${options.sessionId}/se/bidi`;
      k.value.capabilities['se:gridWebSocketUrl'] = `${constants.WS_PROXY_SCHEME}://${wsProxyHost}:${constants.WS_PROXY_PORT}/session/${options.sessionId}`;
    } else {
      delete k.value.capabilities['se:gridWebSocketUrl'];
      delete k.value.capabilities.webSocketUrl;
    }
  }
  return { options, k };
}

exports.updateBidiCapabilities = updateBidiCapabilities;

function getHostNameAndCapabilities(request, response) {
  request.start_up_request_time = new Date();
  var hd = request.headers;
  var inRequest = request;
  var inResponse = response;
  var same_machine_retry = 0;

  requestlib.readRequest(request)
  .then((data) => {
    helper.addToConsoleTimes(request, 'caps-parsing');
    var caps = {};
    try {
      caps = JSON.parse(data);
    } catch(e) {
      return startSession.invalidJSON(request, response, data, e);
    }
    let rawCapabilities = {};
    if(caps.desiredCapabilities) {
      rawCapabilities = JSON.parse(JSON.stringify(caps.desiredCapabilities));
      rawCapabilities = capabilitiesRedactor.redactCapabilities(rawCapabilities, constants.PII_CAPABILITIES);
      rawCapabilities = capabilitiesRedactor.redactCapabilities(rawCapabilities, constants.BROWSER_OPTIONS);
      rawCapabilities = capabilitiesRedactor.redactW3CCapabilities(rawCapabilities, constants.PII_CAPABILITIES, constants.BSTACK_OPTIONS_PII_CAPABILITIES);
    }

    var app_testing = false;
    var isW3CAppAutomateSession = w3cHelper.isW3CAppAutomateSession(caps);

    if (caps['desiredCapabilities'] && !caps['desiredCapabilities'].hasOwnProperty('app')) {
      if (caps['desiredCapabilities'].hasOwnProperty('app_url')) {
        caps['desiredCapabilities']['app'] = caps['desiredCapabilities']['app_url'];
      } else if (caps['desiredCapabilities'].hasOwnProperty('appUrl')) {
        caps['desiredCapabilities']['app'] = caps['desiredCapabilities']['appUrl'];
      }
    }

    if(isW3CAppAutomateSession || w3cHelper.doW3CHasCapability(caps, 'app') || (caps['desiredCapabilities'] && caps['desiredCapabilities'].hasOwnProperty('app'))) {
      request.is_app_automate_session = true;
      app_testing = true;
    }

    // changing rawCapabilities, to enable sending (W3C) capabilities to eds
    try {
      let desiredCapabilities = rawCapabilities,
        capabilities = {};
      if(caps.capabilities) {
        capabilities = JSON.parse(JSON.stringify(caps.capabilities));
        capabilities = capabilitiesRedactor.redactW3CCapabilities(capabilities, constants.PII_CAPABILITIES, constants.BSTACK_OPTIONS_PII_CAPABILITIES);
        capabilities = capabilitiesRedactor.redactW3CCapabilities(capabilities, constants.BROWSER_OPTIONS, []);
      }
      rawCapabilities = {desiredCapabilities, capabilities};
    } catch (err) {
      HubLogger.newCGLogger('W3C_EDS_ERROR', err.message, LL.REQUEST, 'START_FLOW_ERROR');
    }

    var basic_auth = {};

    if (!startSession.checkDesiredCaps(request, response, caps, data)) return;

    try {
      var auth = Buffer.from(hd['authorization'].split(' ')[1], 'base64');
      basic_auth = auth.toString('ascii').split(':');
    } catch(e) {
      basic_auth[0] = startSession.getBrowserstackSpecificCapability(caps, 'browserstack.user', 'userName');
      basic_auth[1] = startSession.getBrowserstackSpecificCapability(caps, 'browserstack.key', 'accessKey');
    }

    HubLogger.newCGLogger('REQUEST_START', `Selenium start request received - ${request.url}. Data-Size - ${data.length}`, LL.REQUEST, 'selenium_start_request', false, undefined, basic_auth[0]);

    // Deleting here so that we dont store this
    caps = startSession.deleteBrowserstackSpecificCapability(caps, 'browserstack.user', 'userName');
    caps = startSession.deleteBrowserstackSpecificCapability(caps, 'browserstack.username', 'userName');
    caps = startSession.deleteBrowserstackSpecificCapability(caps, 'browserstack.userName', 'userName');
    caps = startSession.deleteBrowserstackSpecificCapability(caps, 'browserstack.key', 'accessKey');
    caps = startSession.deleteBrowserstackSpecificCapability(caps, 'browserstack.accessKey', 'accessKey');

    // invalid cap, blacklisting it for now. we can remove this or whitelist  specific caps later
    constants.blacklistedCaps.forEach((blacklistedCap) => {
      caps = startSession.deleteBrowserstackSpecificCapability(caps, blacklistedCap, blacklistedCap);
    });

    if (!startSession.checkBasicAuth(request, response, basic_auth, caps)) return;

    if (blockedUser(request, response, basic_auth[0])) return;

    inResponse = response = chunkedResponse(response);
    caps = startSession.addW3CFlagIfSpecifiedBrowserstackOptions(caps, app_testing);
    caps['desiredCapabilities']['browserstack.region'] = constants.region;
    caps['desiredCapabilities']['browserstack.is_hub_canary'] = helper.isHubCanary;


    // 1. Merge chromeOptions to goog:chromeOptions
    // 2. Merge ms:edgeOptions into chromeOptions.
    //  Currently ms:edgeOptions is same as chromeOptions. See: https://github.com/SeleniumHQ/selenium/commit/646b49a5acd8cc896408b8dfaaa631e71242f4b8
    let chromeOptions = {};
    constants.CHROME_OPTIONS_PREFERENCE.forEach((options) => {
      if (helper.isHash(caps['desiredCapabilities'][options])) {
        helper.deepMerge(caps['desiredCapabilities'][options], chromeOptions);
        delete caps['desiredCapabilities'][options];
      }
    });
    if(Object.keys(chromeOptions).length > 0) {
      delete chromeOptions['binary'];
      caps['desiredCapabilities']['chromeOptions'] = chromeOptions;
    }
    chromeOptions = null; // no longer needed

    if (helper.isHash(caps.capabilities)) {
      constants.CHROME_OPTIONS_PREFERENCE.forEach((options) => {
        if (helper.isHash(caps.capabilities.alwaysMatch) && helper.isHash(caps.capabilities.alwaysMatch[options])) {
          delete caps.capabilities.alwaysMatch[options].binary;
        }
        if (Array.isArray(caps.capabilities.firstMatch) && caps.capabilities.firstMatch.length > 0 && helper.isHash(caps.capabilities.firstMatch[0][options])) {
          delete caps.capabilities.firstMatch[0][options].binary;
        }
      });
      if (Array.isArray(caps.capabilities.firstMatch) && caps.capabilities.firstMatch.length > 0 && helper.isHash(caps.capabilities.firstMatch[0][constants.BSTACK_OPTIONS])) {
        if (helper.isHash(caps.capabilities.firstMatch[0][constants.BSTACK_OPTIONS]['headerParams'])) {
          caps.capabilities.firstMatch[0][constants.BSTACK_OPTIONS]['headerParams'] = JSON.stringify(caps.capabilities.firstMatch[0][constants.BSTACK_OPTIONS]['headerParams']);
        }
      }
      if (helper.isHash(caps.capabilities.alwaysMatch) && helper.isHash(caps.capabilities.alwaysMatch[constants.BSTACK_OPTIONS])) {
        if (helper.isHash(caps.capabilities.alwaysMatch[constants.BSTACK_OPTIONS]['headerParams'])) {
          caps.capabilities.alwaysMatch[constants.BSTACK_OPTIONS]['headerParams'] = JSON.stringify(caps.capabilities.alwaysMatch[constants.BSTACK_OPTIONS]['headerParams']);
        }
      }
    }

    if (caps['desiredCapabilities'] && helper.isHash(caps['desiredCapabilities']['browserstack.headerParams'])){
      caps['desiredCapabilities']['browserstack.headerParams'] = JSON.stringify(caps['desiredCapabilities']['browserstack.headerParams']);
    }
    if (caps['desiredCapabilities'] && caps['desiredCapabilities']['bstack:options'] &&  helper.isHash(caps['desiredCapabilities']['bstack:options']['headerParams'])){
      caps['desiredCapabilities']['bstack:options']['headerParams'] = JSON.stringify(caps['desiredCapabilities']['bstack:options']['headerParams']);
    }

    constants.CHROME_OPTIONS_PREFERENCE.filter(options => options != 'chromeOptions').forEach(options => delete caps['desiredCapabilities'][options]);

    delete caps['desiredCapabilities']['ie.ensureCleanSession'];
    caps['desiredCapabilities']['acceptSslCert'] = caps['desiredCapabilities']['acceptSslCert'] || caps['desiredCapabilities']['acceptSslCerts'] || caps['desiredCapabilities']['acceptInsecureCerts'] || false;
    caps['desiredCapabilities']['detected_language'] = request.language = getUserAgent(request);
    caps['desiredCapabilities'] = typesafeObject(caps['desiredCapabilities']);
    var realMobile = caps['desiredCapabilities']['realMobile'] || false;
    if(caps['desiredCapabilities']['device'] && realMobile.toString() == 'false'){
      delete caps['desiredCapabilities']['version'];
    }
    var rails_omitted_caps = {};
    for (const index in constants.rails_omitted_caps) {
      if(caps['desiredCapabilities'][constants.rails_omitted_caps[index]]){
        rails_omitted_caps[constants.rails_omitted_caps[index]] = caps['desiredCapabilities'][constants.rails_omitted_caps[index]];
        delete caps['desiredCapabilities'][constants.rails_omitted_caps[index]];
      }
    }
    for (const index in constants.rails_redacted_caps) {
      if(caps['desiredCapabilities'][constants.rails_redacted_caps[index]]){
        rails_omitted_caps[constants.rails_redacted_caps[index]] = caps['desiredCapabilities'][constants.rails_redacted_caps[index]];
        caps['desiredCapabilities'][constants.rails_redacted_caps[index]] = "[REDACTED]";
      }
    }

    add_to_redacted_caps(rails_omitted_caps, caps['desiredCapabilities'], 'firefoxOptions', 'profile', 'mozfirefoxProfile');
    add_to_redacted_caps(rails_omitted_caps, caps['desiredCapabilities'], 'moz:firefoxOptions', 'profile', 'mozfirefoxProfile');
    truncateHosts(caps, rails_omitted_caps);

    if(caps['desiredCapabilities']['chromeOptions'] && caps['desiredCapabilities']['chromeOptions']['extensions']){
      rails_omitted_caps['chromeExtension'] = caps['desiredCapabilities']['chromeOptions']['extensions'];
      delete caps['desiredCapabilities']['chromeOptions']['extensions'];
    }

    if(caps['desiredCapabilities'].hasOwnProperty('browserstack.useW3c')) {
      caps['desiredCapabilities']['browserstack.use_w3c'] = caps['desiredCapabilities']['browserstack.useW3c'];
      delete caps['desiredCapabilities']['browserstack.useW3c'];
    }
    helper.removeAccessibilityAutomationPresignedURL(caps, rails_omitted_caps);
    const clientDialect = detectClientDialect(caps);
    // Check W3C capbilities only when browserstack.use_w3c is passed and it is true in desiredCapabilities.
    const shouldUseW3C = startSession.getBrowserstackSpecificCapability(caps, 'browserstack.use_w3c', 'useW3C') || '';

    if (app_testing) {
      if (isW3CAppAutomateSession.toString() !== 'true') {
        delete caps['capabilities'];
      }
    } else {
      if (shouldUseW3C.toString() !== 'true') {
        delete caps['capabilities'];
      }
    }

    // TODO MOVE ALL THIS TO RAILS
    // capabilities key contains the W3C capabilities passed by the Client.
    // We are going to pack them inside desiredCapabilites as W3C_capabilities.
    // In Rails, we will verify/validate the W3C_capabilities.
    // If the response from Rails, contains W3C_capabilities, then it means that the W3C caps were valid, and we will forward them to the JAR.
    // If the reponse does not have W3C_capabilities, that means they were invalid, therefore 'desiredCapabilities' will be forwarded to the JAR as is.
    caps['desiredCapabilities']['new_bucketing'] = constants.newBucketing;
    if (helper.isHash(caps['capabilities'])) {

      // Keeping only the first element of firstMatch, given it is an object
      if(Array.isArray(caps['capabilities']['firstMatch']) && helper.isHash(caps['capabilities']['firstMatch'][0]) ) {
        caps['capabilities']['firstMatch'] = [ caps['capabilities']['firstMatch'][0] ];
      } else {
        caps['capabilities']['firstMatch'] = [{}];
      }

      if(!helper.isHash(caps['capabilities']['alwaysMatch'])) {
        caps['capabilities']['alwaysMatch'] = {};
      }
      add_to_omitted_caps(rails_omitted_caps, caps['capabilities']['firstMatch'][0], 'goog:chromeOptions', 'extensions', 'chromeExtension');
      add_to_omitted_caps(rails_omitted_caps, caps['capabilities']['alwaysMatch'], 'goog:chromeOptions', 'extensions', 'chromeExtension');
      add_to_redacted_caps(rails_omitted_caps, caps['capabilities']['firstMatch'][0], 'moz:firefoxOptions', 'profile', 'mozfirefoxProfile');
      add_to_redacted_caps(rails_omitted_caps, caps['capabilities']['alwaysMatch'], 'moz:firefoxOptions', 'profile', 'mozfirefoxProfile');

      caps['desiredCapabilities']['W3C_capabilities'] = caps['capabilities'];
    }

    helper.addToQueueingStatsHoothoot(app_testing, request.id);

    var local = false;
    if((caps['desiredCapabilities']['browserstack.local'] && caps['desiredCapabilities']['browserstack.local'].toString() == 'true') || (caps['desiredCapabilities']['browserstack.tunnel']  && caps['desiredCapabilities']['browserstack.tunnel'].toString() == 'true') || (caps['desiredCapabilities']['bstack:options'] && caps['desiredCapabilities']['bstack:options']['local'] && caps['desiredCapabilities']['bstack:options']['local'].toString() == 'true'))
      local = true;

    var username_from_basic_auth = basic_auth[0];

    if (username_from_basic_auth) {
      username_from_basic_auth = username_from_basic_auth.toString();
    }

    let product_package = '';
    if (hd['host'].indexOf(constants.FUNCTIONAL_TESTING_DOMAIN) > -1) {
      product_package = constants.FUNCTIONAL_TESTING_PACKAGE_NAME;
    }

    var username = (username_from_basic_auth && username_from_basic_auth.split('-')[0]) ? username_from_basic_auth.split('-')[0] : username_from_basic_auth;
    var user_ci_plugin  = (username_from_basic_auth && username_from_basic_auth.split('-')[1]) ? username_from_basic_auth.split('-')[1] : '';

    // https://browserstack.atlassian.net/browse/APS-9388
    let instrumentationData = { user: username, user_ci: getClientAddress(request), capability_type: 'desiredCapabilities' };
    const desiredCapsString = sanitiseRailsCapsLimit(JSON.stringify(caps.desiredCapabilities), caps.desiredCapabilities, instrumentationData);
    instrumentationData.capability_type = 'rawCapabilities';
    const rawCapabilitiesString = sanitiseRailsCapsLimit(JSON.stringify(rawCapabilities), rawCapabilities, instrumentationData);

    let post_params = {
      u: username,
      password: basic_auth[1],
      auth: constants.railstoken,
      start: 'true',
      local,
      desiredCapabilities: encodeURIComponent(desiredCapsString),
      rawCapabilities: encodeURIComponent(rawCapabilitiesString),
      requestReceivedAt: request.requestReceivedAt,
      requestId: request.id,
      ci: getClientAddress(request),
      user_ci_plugin: user_ci_plugin,
      isAppAutomate: app_testing,
      isw3cSession: isW3CAppAutomateSession,
      product_package: product_package,
      isFunctionalTesting: product_package === constants.FUNCTIONAL_TESTING_PACKAGE_NAME,
      hub_hostname: hd.host,
    };

    if (isTrueString(helper.nestedKeyValue(caps, ['desiredCapabilities', 'isPuppeteer']))) {
      post_params = { ...post_params, framework: 'puppeteer' };
    } else if (isTrueString(helper.nestedKeyValue(caps, ['desiredCapabilities', 'isPlaywright']))) {
      post_params = { ...post_params, framework: 'playwright' };
    } else if (isTrueString(helper.nestedKeyValue(caps, ['desiredCapabilities', 'isDetox']))) {
      post_params = { ...post_params, framework: 'detox' };
    }

    helper.addToConsoleTimes(request, 'caps-parsing-end');

    var post_params_str = JSON.stringify(post_params);
    var post_browserstack_callback = function(request, response, output, hostname, proxy_request_1, options, queue, endSession, queueUtilized, queueLimit, parallelUtilized, parallelLimit) {
      // proxy_request_1 exists for legacy purposes
      if(queue) {
        if (!response.clientOnline()) {
          const isMobile = helper.isMobile(post_params);
          const hoothootPlatform = post_params.isAppAutomate ? 'all' : isMobile ? 'mobile' : 'desktop';
          HubLogger.hoothoot_user.uniqueUserEvent(post_params['u'], (post_params.isAppAutomate ? 'app-automate' : 'automate'), 'client_disconnect', hoothootPlatform, helper.getHubCanaryTag());
          if (!post_params.isAppAutomate) HubLogger.hoothoot_use.emit('automate_errors_data', 1, { event_type: 'client_disconnect', platform: hoothootPlatform, product: 'automate', hub_region: constants.region});
          if (post_params["automation_session_id"]) {
            helper.pushToCLS('user_disconnected_before_retry', {
              session_id: post_params["automation_session_id"],
              user_id: post_params['u'],
              firecmd_attempt: options.firecmd_attempt,
              start_req_attempt: options.start_attempt,
              retry: options.attempt,
              requestId: request.id,
              message: 'Post rails queue'
            }, post_params.isAppAutomate);
          }
          helper.sendToEDS({
            kind: post_params.isAppAutomate ? Events.APP_AUTOMATE_ERROR_DATA : Events.AUTOMATE_ERROR_DATA,
            error_code_str: "user-not-online-pipeline",
            error_message: {
              caps: (post_params.desiredCapabilities) ? JSON.parse(decodeURIComponent(post_params.desiredCapabilities)) : 'undefined',
              firecmd_attempt: options.firecmd_attempt,
              start_req_attempt: options.start_attempt,
              retry:  options.attempt,
              username: post_params['u']
            },
            raw_capabilities: (post_params.rawCapabilities) ? JSON.parse(decodeURIComponent(post_params.rawCapabilities)) : 'undefined',
            request_received_at: request.requestReceivedAt,
            request_id: request.id,
          });
          HubLogger.miscLogger('PostBrowserStackCallback:', 'Dropping queue as client is not longer waiting on it. Queue Id: ' + options.queue_id, LL.INFO);
          helper.redisClientSecond.hincrby(constants.USER_TERMINATED_SESSIONS, `queue::${queueHandler.getQueueProduct(post_params)}dropped::${post_params.u}`, 1);
          delete queued[options.queue_id];
          helper.respondWithError(request, response, 'ClientNotOnline', true);
          helper.addToConsoleTimes(request, 'time-in-queue-end');
          return;
        }
        queueHandler.updateQueue(post_params['u'], queueUtilized, queueLimit, parallelUtilized, parallelLimit, post_params.isAppAutomate, post_params.isFunctionalTesting);
        var sessionId = options.queue_id;
        queued[sessionId] = queued[sessionId] || 0;
        var railsRetriesLimit = undefined;
        var railsRetryReason = undefined;
        if (helper.isHash(options['queueOptionsInRailsResponse'])) {
          railsRetriesLimit = options['queueOptionsInRailsResponse']['queue_max_times'];
          railsRetryReason = options['queueOptionsInRailsResponse']['reason'];
        }

        if (railsRetryReason === "region-down") {
          // This sessionId was affected due to region-down
          regionDownQueued.add(sessionId);
        } else {
          if (regionDownQueued.has(sessionId)) {
            // The sessionId was affected by region-down but after region is up, it got a failure with different reason
            // Need to now reset the retry iterator to 0 as the new max_retry_count would be decreased
            queued[sessionId] = 0;
            regionDownQueued.delete(sessionId);
          }
        }
        var retries = railsRetriesLimit || ( constants.QUEUED_REQUEST_EXPONENTIAL_DELAY.QUEUE_MAX_WAIT / constants.QUEUED_REQUEST_EXPONENTIAL_DELAY.HARD_RETRY_REQUEST_DELAY );
        if (railsRetryReason && railsRetryReason === "nta") {
          retries = railsRetriesLimit || ( constants.QUEUED_REQUEST_EXPONENTIAL_DELAY.QUEUE_MAX_WAIT / constants.NTA_RETRY_EXPONENTIAL_DELAY.NTA_RETRY_DEFAULT_DELAY );
        }
        if (typeof caps['desiredCapabilities']['browserstack.queue.retries'] != 'undefined')
           retries = caps['desiredCapabilities']['browserstack.queue.retries'];

        if (queued[sessionId] < retries) {
          HubLogger.miscLogger('NTA: Queuing user ', sessionId + ', ' + queued[sessionId], LL.INFO);
          if(queued[sessionId] === 0) {
            helper.addToConsoleTimes(request, 'time-in-queue');
          }
          queued[sessionId]++;
          if (queued[sessionId] >= (caps['desiredCapabilities']['browserstack.queue.retries'] || retries))
            post_params['queue_again'] = 'false';
          else
            post_params['queue_again'] = 'true';
          post_params['queue_times'] = queued[sessionId];
          post_params['queue_id'] = sessionId;
          post_params['queue_reason'] = options.queue_reason;

          var tmpOptions = {
            callback: post_browserstack_callback,
            language: request.language,
            post_params,
            rails_omitted_caps,
            queue_id: options.queue_id,
            counter: queued[sessionId],
            indexCounter: options.indexCounter || 0,
            attempt: options.attempt,
            firecmd_attempt: options.firecmd_attempt,
            start_attempt: options.start_attempt,
            reason: railsRetryReason,
          };
          helper.queueRequest(inRequest, inResponse, tmpOptions, queued[sessionId]);
          return;
        } else {
          HubLogger.miscLogger('User Queue', 'Exhausted the retries for session ' + sessionId + '. Giving up.', LL.INFO);
          helper.addToConsoleTimes(request, 'time-in-queue-end');
          delete queued[sessionId];
          regionDownQueued.delete(sessionId);
          endSession();
          return;
        }
      }
      if ('queue_id' in options) {
        delete queued[options.queue_id];
        regionDownQueued.delete(queued[options.queue_id]);
      }
      var data = output.data;

      HubLogger.miscLogger('START-SESSION', basic_auth[0] + ' : Browser / Device : ' + caps['desiredCapabilities']['browserName'], LL.INFO);

      if (isNotUndefined(options.browserstackParams['browserstack.ai_enabled_session']) && isTrueString(options.browserstackParams['browserstack.ai_enabled_session'])) {
        const groupAIEnabled = (isTrueString(options.browserstackParams['browserstack.ai_enabled_session'])) ? ( JSON.parse(options.browserstackParams['browserstack.ai_details']) || {} ).tcg_ai_enabled : false;
        const aiAuthData = `{"data": {"sessionId": "${options.rails_session_id}", "userId": "${options.user_id}", "groupAIEnabled": "${groupAIEnabled || false}"}}`;
        AICommandHelper.makeRequestTcg(TCG_ENDPOINTS.AUTH.method, TCG_ENDPOINTS.AUTH.path, aiAuthData, TCG_HEADERS, AICommandHelper.getTcgEndpoint())
          .then((response) => {
            const tcgData = JSON.parse(response.data);
            if ( response.statusCode !== 200 || isUndefined(tcgData) || (tcgData.success || false).toString() !== "true" ) {
              throw(new Error(tcgData));
            }
            HubLogger.newCGLogger("AI_AUTH_REQUEST", `AI Auth request success with ${tcgData}`, LL.DEBUG, options.rails_session_id);
          })
          .catch((err) => HubLogger.newCGLogger("AI_AUTH_REQUEST", `AI Auth request failed with: ${err.message}`, LL.ERROR, options.rails_session_id)
          );
      }
      var k = {};
      if (data && data != '') {
        try {
          k = JSON.parse(data);
        } catch (e) {
          if(data.match(/appium/i) || ('' + output.statusCode).indexOf('30') > -1) {
            data = '';
          } else {
            HubLogger.exceptionLogger('JSON Parse Error : ' + e.toString() + ' ' + data, hostname, request.url);
          }
        }
      }

      for (var index in constants.output_omitted_caps){
        if(k['value'] != undefined && k['value'][constants.output_omitted_caps[index]] != undefined){
          delete k['value'][constants.output_omitted_caps[index]];
        }
      }
      if(k['value'] != undefined && ['win7', 'win8.1', 'win10', 'win11'].includes(options.bsCaps['orig_os'])){
        k['value']['platform'] = 'WINDOWS';
      }

      options.clientDialect = clientDialect;
      let remoteJarSessionID = k.sessionId;
      if(k.value && k.value.sessionId && !remoteJarSessionID && !k.status) {
        remoteJarSessionID = k.value.sessionId;
        options.dialect = 'W3C';
      } else {
        options.dialect = 'OSS';
      }

      const sub_region = options.browserstackParams["browserstack.terminal_sub_region"];
      const isSDKSession = isNotUndefined(options.browserstackParams["browserstack.browserstackSDK"]);
      const isValidSDKSession = isSDKSession && helper.validSDKVersionForDynamicHubAllocation(options.browserstackParams["browserstack.browserstackSDK"]);
      const optimalHubUrl = constants.SUB_REGION_HUB_URL_MAPPING[sub_region];
      let wsProxyHost = constants.WS_PROXY_HOST;

      if (remoteJarSessionID && isValidSDKSession && optimalHubUrl) {
        wsProxyHost = constants.CDP_SUB_REGION_HUB_URL_MAPPING[sub_region] || constants.WS_PROXY_HOST;
        options.optimalHubUrl = optimalHubUrl;
        if(options.dialect === 'W3C') {
          k.value.capabilities['optimalHubUrl'] = optimalHubUrl;
        } else {
          k.value['optimalHubUrl'] = optimalHubUrl;
        }
      }

      const updatedSeCdp = updateCdpCapabilities(options, k, wsProxyHost);
      options = updatedSeCdp.options;
      k = updatedSeCdp.k;

      const updatedWebSocket = updateBidiCapabilities(options, k, wsProxyHost);
      options = updatedWebSocket.options;
      k = updatedWebSocket.k;

      data = data ? JSON.stringify(k) : '';

      if (!output.headers['location'] && !remoteJarSessionID) {
        clearInterval(options.relaxed_interval);
        HubLogger.exceptionLogger(basic_auth[0] + ' : Error:' + data, hostname, request.url);
        var device = options.browserstackParams['realMobile'] || false;
        var is_apple_os = device && helper.isAppleOs(options.bsCaps['orig_os']);
        var is_real_android = device && options.bsCaps['orig_os'] == 'android' && options.realMobile;
        var appTesting = typeof options.bsCaps['app'] !== 'undefined';
        var opts = browserstack.getLoggingOpts(options.bsCaps, options.browserstackParams);
        opts.indexCounter = options.indexCounter || 0;
        HubLogger.exceptionLogger(basic_auth[0] + ' : Error:' + data, hostname, request.url);
        opts.errorStack = JSON.stringify(k);
        var error_message = k.value ? k.value.message : 'start-error';
        var is_bad_profile = error_message && error_message.match(/Illegal base64 character/i) && caps['desiredCapabilities']['browser'] && caps['desiredCapabilities']['browser'].match(/firefox/i);
        var is_real_bsf_retry = error_message && error_message.match(/unlock|adb|offline/i) && !appTesting;
        var is_bsf_retry = device && same_machine_retry != 0 && !is_real_bsf_retry;
        var is_same_retry = same_machine_retry < 1 && (is_apple_os || !options.avoidAppium) && !appTesting;
        var is_bsf_same_retry_done = options.post_params['automation_session_id'] && (same_machine_retry != 0 || !is_same_retry);
        var get_jar_options = {
            method: 'GET',
            path: '/wd/hub/status',
            hostname: options.rproxyHost,
            port: options.port,
          };
        requestlib.appendBStackHostHeader(hostname, get_jar_options.headers);

        if(is_bsf_same_retry_done || is_bsf_retry || appTesting){
          var custom_error_msg = is_bad_profile ? 'start-error-badprofile' : 'start-error';
          if(options.realMobile && !appTesting && opts.os_version == 'iphone' && options.bsCaps && options.bsCaps.mobile && options.bsCaps.mobile.version) {
            opts.os_version = options.bsCaps.mobile.version;
          }
          helper.addToConsoleTimes(request, `post-browserstack-end`);
          HubLogger.nodeErrorHandler(request, response, data, `${hostname}:${options.port}`, options.sessionId, custom_error_msg, error_message, undefined, opts, options, undefined, function() {
            helper.startUpFailureCallbackHandler(options, device, appTesting, k);
          });
        }
        else {
          error_message = k.value && k.value.message ? k.value.message : 'start-error';
          opts.name = hostname;
          post_params = JSON.parse(post_params_str);
          if (is_same_retry && ( !( is_real_android && !appTesting ) )) {
            HubLogger.seleniumStats('browser-startup-failure-same-machine', options, (error_message != null) ? error_message.toString() : '', data, 'start-error');

            requestlib.call(get_jar_options)
            .then((response) => {
              HubLogger.miscLogger('START-ERROR-JAR-DATA', 'SessionID: ' + options.sessionId + ' Port: ' + options.port + ' JAR details: ' + response.data, LL.ERROR);
            })
            .catch((err) => {
              HubLogger.miscLogger('START-ERROR-JAR-DATA-FAILED', err.toString(), LL.ERROR);
            });

            same_machine_retry += 1;
            helper.pushToCLS('browser_startup_failure_retry_same_machine', {
              session_id: options.sessionId,
              user_id: post_params['u'],
              response: data,
            });
            HubLogger.miscLogger('BSF-SAME-MACHINE-RETRY', 'Selenium: ' + options.sessionId + ' Terminal: ' + hostname, LL.INFO);
            options.attempt = constants.maxAllowedDifferentMachineRetries + 1;
            setTimeout(function(){
              browserstack.startSession(request, response, options.post_data, options.host_name, options.port, post_browserstack_callback, options);
            }, constants.SAME_RETRY_TIMEOUT);
          } else {
            HubLogger.seleniumStats('browser-startup-failure-retry', options, (error_message != null) ? error_message.toString() : '', data, 'start-error');

            requestlib.call(get_jar_options)
            .then((response) => {
              HubLogger.miscLogger('START-ERROR-RETRY-JAR-DATA', 'SessionID: ' + options.sessionId + ' Port: ' + options.port + ' JAR details: ' + response.data, LL.INFO);
            })
            .catch((err) => {
              HubLogger.miscLogger('START-ERROR-RETRY-JAR-DATA-FAILED', err.toString(), LL.INFO);
            });

            helper.pushToCLS('browser_startup_failure_retry', {
              session_id: options.sessionId,
              user_id: post_params['u'],
              response: data,
            });

            if(options.bsCaps && options.browserstackParams && options.browserstackParams.realMobile && options.browserstackParams.realMobile.toString() === 'true' && options.bsCaps.device && is_real_android) {
              post_params['hardReleaseDeviceUDID'] = options.bsCaps.udid;
              options.attempt = constants.maxAllowedDifferentMachineRetries + 1;
              same_machine_retry += 1;
            } else {
              same_machine_retry = 0;
            }
            post_params['hardRelease'] = hostname;
            post_params['automation_session_id'] = options.sessionId;
            if(options.realMobile && device) post_params['hardReleaseDevice'] = ( options && options.bsCaps && options.bsCaps.udid ) ? ( options.bsCaps.udid ) : '';
            HubLogger.miscLogger('BSF-RETRY', 'Selenium: ' + options.sessionId + ' Terminal: ' + hostname, LL.INFO);

            var return_value = browserstack.postBrowserStack('', post_params, request, response, post_browserstack_callback, rails_omitted_caps, undefined, undefined, undefined, undefined, undefined, undefined, undefined, true);

            // in case user_disconnected_before_retry error is received here, the session needs to be ended with 'start error'.
            if(return_value === "user_disconnected_before_retry") {
              custom_error_msg = is_bad_profile ? 'start-error-badprofile' : 'start-error';
              HubLogger.nodeErrorHandler(request, response, data, `${hostname}:${options.port}`, options.sessionId, custom_error_msg, error_message, undefined, opts, options, undefined, function() {
                helper.startUpFailureCallbackHandler(options, device, appTesting, k);
              });
            }
          }
        }
      } else {
        //HubLogger.requestLogger(hostname, proxy_request_1, post_params["desiredCapabilities"], "/wd/hub/session", true, undefined, LL.REQUEST);
        HubLogger.jarRequestLogger(hostname, output, HubLogger.redactHashForKeys(data), '/wd/hub/session', true, undefined, LL.REQUEST);

        var key = output.headers['location'] ? output.headers['location'] : remoteJarSessionID;

        if(k['value'] && k['value']['wda_port']) {
          caps['desiredCapabilities']['wda_port'] = k['value']['wda_port'];
        } else {
          const wdaPortForW3C = helper.nestedKeyValue(helper.getParsedObjectOrEmpty(options.post_data), ['desiredCapabilities', 'wda_port']);
          if (wdaPortForW3C) {
            caps['desiredCapabilities']['wda_port'] = wdaPortForW3C;
          }
        }

        var host_params = addNewMappingInRegistry(key, hostname, caps['desiredCapabilities'], options, request, basic_auth, function(){
          helper.addToConsoleTimes(request, `post-browserstack-end`);
          HubLogger.miscLogger('Session-mapping', 'Dialect: ' + options.dialect + ' Selenium: ' + host_params.key + ' Rails: ' + host_params.rails_session_id, LL.INFO);
          data = redactor.redactSensitiveDataFromSessionInfo(data);
          // Scrub Platformversion from raw logs only for iOS sessions
          data = redactor.deletePlatformVersion(data);
          data = replaceSessionID(data, host_params.key, host_params.rails_session_id);

          delete caps['desiredCapabilities']['browserstack.region'];
          delete caps['desiredCapabilities']['browserstack.is_hub_canary'];
          delete caps['desiredCapabilities']['browserstack.appStoreConfiguration'];
          let truncatedHosts = restoreHosts(caps, options);
          var requestLogline = helper.s3LogFormat('REQUEST', helper.getDate(), '[' + helper.getDate() + '] ' + request.method + ' ' + request.url.replace('/wd/hub', '') + ' '+ JSON.stringify(caps)) + '\r\n';
          if (truncatedHosts){
            caps.desiredCapabilities['browserstack.hosts'] = truncatedHosts;
          }
          var startLogline = helper.s3LogFormat('START_SESSION', helper.getDate(), '');

          if (!isCDP(host_params)) {
            HubLogger.sessionLog(host_params, 'REQUEST_START', requestLogline + startLogline);
          }

          if(remoteJarSessionID) {
            requestLogline = helper.s3LogFormat('REQUEST', helper.getDate(), '[' + helper.getDate() + '] GET /session/' + host_params.rails_session_id) + '\r\n';
            var responseLogline = helper.s3LogFormat('RESPONSE', helper.getDate(), data.toString('utf-8'));
            if (!isCDP(host_params)) {
              HubLogger.sessionLog(host_params, 'REQUEST_RESPONSE', requestLogline + responseLogline);
            }
          }

          if(!(data.length == 0 && output.headers['location'])) {
            regSession(host_params.rails_session_id, hostname, host_params.key, host_params.start_time_ts, data.toString('utf-8'), post_params.isAppAutomate, {
              recoveredBrowserMobError: options.recoveredBrowserMobError,
              selenium_version: options.bsCaps['selenium_version'],
              build_number: host_params.build_number,
              shouldForceChangeJar: options.shouldForceChangeJar
            });
          }

          var hh = output.headers;
          if(['deflate', 'gzip', 'x-gzip'].indexOf(output.headers['content-encoding']) > -1) {
            delete hh['content-encoding'];
          }
          if (hh['location']) {
            hh['location'] = replaceSessionID(
                              hh['location'].replace(/http:\/\/([0-9.]+):[0-9]*/, ''),
                              host_params.key, host_params.rails_session_id);
          }
          hh['content-length'] = data.length;

          var fetchData = async function (callback) {
            var getReqOpts = {};
            if(data.length == 0 && output.headers['location']){
              helper.addToConsoleTimes(request, 'fetch-data');
              getReqOpts.hostname = options.rproxyHost;
              getReqOpts.port = options.port;
              getReqOpts.headers = requestlib.appendBStackHostHeader(hostname);
              if (key.indexOf('http://') !== 0) {
                getReqOpts.path = key;
              } else {
                getReqOpts.path = '/' + key.split('/').splice(3).join('/');
              }
              HubLogger.miscLogger('REDIRECT', 'Following redirect for ' + key, LL.INFO);
              requestlib.call(getReqOpts)
              .then((res) => {
                helper.addToConsoleTimes(request, 'fetch-data-end');
                data = res.data;
                data = replaceSessionID(data, host_params.key, host_params.rails_session_id);

                var requestLogline = helper.s3LogFormat('REQUEST', helper.getDate(), '[' + helper.getDate() + '] GET /session/' + host_params.rails_session_id) + '\r\n';
                var responseLogline = helper.s3LogFormat('RESPONSE', helper.getDate(), data.toString('utf-8'));

                var outputCaps = {}, stringData = data.toString('utf-8');
                try {
                  outputCaps = JSON.parse(stringData);
                } catch(e) {
                  HubLogger.exceptionLogger('JSON Parse Error : ' + e.toString() + ' ' + stringData, getReqOpts.hostname, getReqOpts.path);
                }

                for (var index in constants.output_omitted_caps){
                  if(outputCaps && outputCaps['value'] && outputCaps['value'][constants.output_omitted_caps[index]]){
                    delete outputCaps['value'][constants.output_omitted_caps[index]];
                  }
                }
                HubLogger.sessionLog(host_params, 'REQUEST_RESPONSE', requestLogline + responseLogline);
                regSession(host_params.rails_session_id, hostname, host_params.key, host_params.start_time_ts, JSON.stringify(outputCaps), post_params.isAppAutomate, {
                  recoveredBrowserMobError: options.recoveredBrowserMobError,
                  selenium_version: options.bsCaps['selenium_version'],
                  shouldForceChangeJar: options.shouldForceChangeJar
                });
                callback();
              })
              .catch((e) => {
                var opts = browserstack.getLoggingOpts(options.bsCaps, options.browserstackParams);
                HubLogger.exceptionLogger('Exception in fetchData: ' + e.type, e.toString());
                HubLogger.nodeErrorHandler(request, response, data, hostname + ':' + options.port, host_params.rails_session_id, 'start-error-emulator', e.toString(), undefined, opts, opts, undefined);
              });
            } else if (host_params.isPuppeteer) {
              const { data: jsonStr, portError } = await fetchDebuggerURL(hostname, data, options);
              if (!portError) {
                data = jsonStr;
                callback();
              } else {
                const err = new Error('Remote debugger port not up');
                HubLogger.exceptionLogger('Exception in fetching the remote debugger port for puppeteer: ' + err.toString());
                HubLogger.nodeErrorHandler(request, response, err, hostname + ":" + options.port, host_params.rails_session_id, "start-error", err.toString(), undefined, {}, options, undefined, function() {
                  HubLogger.addStopToRawLogs(options, host_params.rails_session_id, "Unable to start browser, remote debugger port not up", 1, true, undefined, undefined);
                });
              }
            } else {
              callback();
            }
          };

          var activateBrowser = function() {
            helper.addToConsoleTimes(request, 'activate-browser');
            if ((host_params.browser == 'safari' || (host_params.browser == 'chrome' && parseInt(host_params.browser_version) <= 28)) && (host_params.os.indexOf('mac') > -1) ) {
              var activateSafariURL = util.format('/make_browser_active?browser=%s', host_params.browser);
              requestlib.call({
                method: 'GET',
                hostname: options.rproxyHost,
                port: 45671,
                path: activateSafariURL,
                headers: requestlib.appendBStackHostHeader(hostname),
              })
              .then(sendClientResponse)
              .catch((err) => {
                  HubLogger.miscLogger('Error during response on make browser active', err.toString(), LL.INFO);
                  sendClientResponse();
              });
            }
            else
              sendClientResponse();
          };

          var logTimeAndActivateBrowser = function() {
            helper.addToConsoleTimes(request, 'set-page-load-time-end');
            activateBrowser();
          };

          var sendClientResponse = function(){
            helper.addToConsoleTimes(request, 'activate-browser-end');
            if(typeof options.relaxed_interval !== 'undefined') {
              clearInterval(options.relaxed_interval);
            }
            data = mapResponseToAppropriateDialect(host_params, data);
            response.end(data);

            // set lastResponseTime to now for precise outsideBrowserstackTime
            host_params.lastResponseTime = Date.now();
            pubSub.publish(constants.updateKeyObject, {
              session: host_params.rails_session_id,
              changed: {
                lastResponseTime: host_params.lastResponseTime,
              },
            }, false, false, true);

            helper.timeoutManagerUpdateTimeout(host_params.rails_session_id, host_params);
            helper.pushToCLS('session_started_client_response', {
              user_id: post_params['u'],
              session_id: options.sessionId,
              response: data.toString(),
            });
            helper.PingZombie({
              'sessionid': options.sessionId,
              'start_time': ((new Date()) - request.start_up_request_time),
              'firecmd_time': request.firecmd_time,
              'start_session_time': request.start_session_time,
              'resolution': request.resolution || '',
              'selenium_version': host_params.selenium_version || '',
              'language': request.language,
              'kind': post_params.isAppAutomate ? 'app_automation_session_stats' : 'automation_session_stats',
            });
            helper.addToConsoleTimes(request, 'session-creation-end');
          };

          // Inform chrome-har-capturer to start HAR file recording on Chrome Android.
          // This needs to be done here, because we can only start HAR recording once Browser is started on the device.
          // This part basically ensures that HAR recording has started before sending response of User's  /start request.
          var startHarRecording = function (callback, retry_count) {
            if(retry_count === undefined) {
              retry_count = 1;
            }

            if (retry_count > 4) {
              callback();
              return;
            }

            HubLogger.miscLogger('START_HAR_RECORD_ANDROID', 'Trying to start HAR recording for session: ' + host_params.rails_session_id, LL.INFO);

            const networkLogsOptions = options.browserstackParams["browserstack.networkLogsOptions"] || '';
            const includeHostRegex = options.browserstackParams["browserstack.networkLogsIncludeHosts"] || '';
            const excludeHostRegex = options.browserstackParams["browserstack.networkLogsExcludeHosts"] || '';
            const captureContent = networkLogsOptions ? helper.nestedKeyValueGeneric(networkLogsOptions, ['captureContent'], false, isTrueString) : false;
            /* Add AI session details to send to terminal for CHC */
            const aiSessionDetails = AICommandHelper.getFirecmdTcgOptions(options);
            const path = host_params.os === "android" ? util.format('/start_capture_v2?device=%s&includeHostRegex=%s&excludeHostRegex=%s', host_params.device, encodeURIComponent(includeHostRegex), encodeURIComponent(excludeHostRegex)) : util.format('/start_capture_desktop?captureContent=%s&aiSessionDetails=%s&includeHostRegex=%s&excludeHostRegex=%s', captureContent, encodeURIComponent(aiSessionDetails), encodeURIComponent(includeHostRegex), encodeURIComponent(excludeHostRegex));
            const port = host_params.os === "android" ? constants.chrome_har_capturer_server_port_v2 : constants.chrome_har_capturer_server_port;
            requestlib.call({
              method: 'GET',
              hostname: host_params.rproxyHost,
              port,
              path,
              headers: requestlib.appendBStackHostHeader(host_params.name),
            }).then((res) => {
              HubLogger.miscLogger(
                'START_HAR_RECORD',
                'Started successfully for session: ' + host_params.rails_session_id + 'Response: ' + ( res.statusCode || 'undefined' ) + ' data: ' + ( res.data || 'undefined' ),
                LL.INFO
              );
              callback();
            }).catch((e) => {
              HubLogger.miscLogger('START_HAR_RECORD', 'Har recording start failed ' + retry_count + ' times with ' + e.toString() + ', trying again for session ID: ' + host_params.rails_session_id, LL.INFO);
              startHarRecording(callback, retry_count + 1);
            });
          };

          const shouldSkipResponse = checkResponseOnline(host_params, response);
          if (shouldSkipResponse)
            return;

          const setPageLoadOnFetchDataCallback = function(callback){
            if(!isCDP(host_params) && !post_params.isAppAutomate && ((typeof options.browserstackParams['browserstack.noPageLoadTimeout'] == 'undefined') || options.browserstackParams['browserstack.noPageLoadTimeout'].toString() == 'false')){
              helper.addToConsoleTimes(request, 'set-page-load-time');
              supporting.setPageLoadTimeoutOnTerminal(host_params, logTimeAndActivateBrowser, 200000);
            } else if(options.browserstackParams.network_simulation === 'true'){
              logTimeAndActivateBrowser();
            } else if(callback){
              callback();
            } else{
              sendClientResponse();
            }
          };

          const fetchDataCallback = function () {
            if (host_params.os === 'android' && host_params.browser === 'samsung' && !post_params.isAppAutomate) {
              helper.setContextSamsungMobile(host_params, request, 0, setPageLoadOnFetchDataCallback);
            } else if (options.browserstackParams.fixFlakySamsungTabChrome && options.browserstackParams.fixFlakySamsungTabChrome.toString() === 'true' && !post_params.isAppAutomate) {
              helper.flakySamsungTabChrome(host_params, request, setPageLoadOnFetchDataCallback);
            } else if (!post_params.isAppAutomate && host_params.os.toLowerCase() === 'ios' && host_params.browser.toLowerCase().match(/chromium_(iphone|ipad)/)) {
              helper.setContextIOSChrome(host_params, request, 0, setPageLoadOnFetchDataCallback);
            } else if (!host_params.appTesting && host_params.os === 'android' && options.browserstackParams.network_simulation === 'true') {
              supporting.setNetworkProfile(host_params, options.bsCaps, options.browserstackParams, setPageLoadOnFetchDataCallback);
              if(host_params.networkLogs && !post_params.isAppAutomate) startHarRecording(sendClientResponse);
            } else {
              if ((options.bsCaps['safari.options'] && options.bsCaps['safari.options']['technologyPreview'] && options.bsCaps['safari.options']['technologyPreview'].toString() === 'true') ||
                (host_params.os === 'android' && host_params.browser === 'chrome_android' && post_params.framework === 'playwright')) {
                sendClientResponse();
              } else if (host_params.networkLogs && host_params.os === 'android' && !post_params.isAppAutomate) {
                startHarRecording(setPageLoadOnFetchDataCallback);
              } else if (!post_params.isAppAutomate && host_params.aiEnabledSessions == true && host_params.browser === 'firefox') {
                helper.installHealingExtensionFirefox(host_params, request, 0, setPageLoadOnFetchDataCallback, activateBrowser); // No other option than send 2 CB due to the flow of the code _/\_
              } else {
                setPageLoadOnFetchDataCallback(activateBrowser);
              }
            }
          };
          if (options.browserstackParams["browserstack.networkLogsV2"] === "true") {
            // Start chrome har capture
            startHarRecording(() => { fetchData(fetchDataCallback); });
          } else {
            fetchData(fetchDataCallback);
          }
        });
      }
    };
    var serveQueueFullResponse = function() {
      helper.addToConsoleTimes(request, 'is-queue-full-end');
      HubLogger.miscLogger(`userQueueFull_${post_params['u']}`, 'Stopped start request for user ' + post_params['u'], LL.INFO);
      const isAppAutomate = post_params.isAppAutomate;
      const error = constants.QUEUE_SIZE_EXCEEDED.errorMessage;
      var data = JSON.stringify({value: {message: error, error: error}, sessionId: '', 'status': 13});
      var zombie_kind = isAppAutomate ? 'app_automation_queue_stats' : 'automation_queue_stats';
      const edsKind =  isAppAutomate ? Events.APP_AUTOMATE_ERROR_DATA : Events.AUTOMATE_ERROR_DATA;
      helper.PingZombie({
        'error': constants.queueingRelatedEdsConst.reason,
        'kind': zombie_kind,
        'session_id': post_params['u'],
        'timestamp':(Math.round(new Date().getTime() /1000).toString()),
      });
      helper.redisClient.get(constants.queueingRelatedEdsConst.keyPrefix + post_params['u'], (err, res) => {
        if(res) {
          helper.sendToEDS({
            'kind': edsKind,
            'error_code_str': constants.queueingRelatedEdsConst.reason,
            'request_id': request.id,
            'user': { user_id: res },
            'error_message': error,
            'raw_capabilities': post_params['rawCapabilities'],
            'request_received_at': new Date(),
          });
        }
      });
      helper.respondWithError(request, response, data);
    };

    helper.addToConsoleTimes(request, 'is-queue-full');
    // If queue is full, serve him the message here only
    queueHandler.isQueueFull(post_params['u'], post_params.isAppAutomate, serveQueueFullResponse, function() {
      helper.addToConsoleTimes(request, 'is-queue-full-end');
      helper.addToConsoleTimes(request, 'increase-queue');
      queueHandler.incrQueue(post_params['u'], post_params.isAppAutomate, function() {
        helper.addToConsoleTimes(request, 'increase-queue-end');
        helper.addToConsoleTimes(request, 'post-browserstack');
        browserstack.postBrowserStack('', post_params, request, response, post_browserstack_callback, rails_omitted_caps);
      }, serveQueueFullResponse, post_params.isFunctionalTesting);
    }, post_params.isFunctionalTesting);

  })
  .catch((err) => {
    HubLogger.exceptionLogger('Error in getHostNameAndCapabilities ' + err.stack.toString());
    let dataToSend = JSON.stringify({value: {message: ''}, sessionId: '', 'status': 13});
    HubLogger.instrumentationStats('Error : getHostNameAndCapabilities', {}, '', dataToSend);
    helper.respondWithError(request, response, dataToSend);
    throw err;
  });
}

const fetchDebuggerURL = async (hostname, data, options) => {
  const getWSReqOptions = {
    host: options.rproxyHost,
    port: REMOTE_DEBUGGER_PORT,
    path: '/json/version',
    method: 'GET',
    timeout: 20000,
    headers: requestlib.appendBStackHostHeader(hostname, {
      Host: `localhost:${REMOTE_DEBUGGER_PORT}`,
      // Rproxy-host nginx removes port from host before forwarding
      // This will override host header on nginx for private terminals
      // Firefox checks for the header, will return 400 if not present
      'Cdp-host': `localhost:${REMOTE_DEBUGGER_PORT}`,
    }),
  };
  let portError = false;

  try {
    const response = await requestlib.call(getWSReqOptions, 1);
    const { data: responseData } = response;
    const { webSocketDebuggerUrl } = JSON.parse(responseData.replace(/\n/g, '').replace(/\t/g, ''));
    const jsonData = JSON.parse(data);
    if (jsonData['value']) {
      jsonData['value']['wsURL'] = webSocketDebuggerUrl.replace('localhost', options.rproxyHost).replace('127.0.0.1', options.rproxyHost);
      jsonData['value']['wsHostname'] = hostname;

      // Handling W3C responses
      // Firefox is w3c only, and selenium returns back with a response
      // clubbed inside value (which is w3c format)
      // So changing this to return same format back to terminalAllocation.js
      if (!jsonData['sessionId'] && jsonData['value']['sessionId']) {
        jsonData['sessionId'] = jsonData['value']['sessionId'];
        jsonData['status'] = 0;
        if (jsonData['value']['capabilities']) {
          jsonData['value'] = {...jsonData['value'], ...jsonData['value']['capabilities']};
          delete jsonData['value']['capabilities'];
        }
      }
    }
    data = JSON.stringify(jsonData);
  } catch (err) {
    HubLogger.exceptionLogger('Error in JSON parsing ' + err.toString());
    portError = true;
  }

  return {
    data,
    portError,
  };
};

function addNewMappingInRegistry(buff, hostname, caps, options, request, basic_auth, callback) {
  var sessionIdTokens = buff.split('/');
  var sessionId = sessionIdTokens[sessionIdTokens.length-1];
  if (sessionId == '') {
    sessionId = sessionIdTokens[sessionIdTokens.length-2];
  }
  var client_ip = getClientAddress(request);
  if (sessionId) {
    var bsCaps = options.bsCaps;
    var browserstackParams = options.browserstackParams;
    var data = browserstackParams['browserstack.aws.save'].split('/'); //[0]: bucket, [1]: rails id
    var rails_session_id = data[1];
    const build_number = options['build_number'];
    var device = options.realMobile ? bsCaps['udid'] : false;
    if (constants.global_registry[rails_session_id]) {
      if (Object.keys(constants.global_registry[rails_session_id]).length == 0) {
        constants.global_registry[rails_session_id] = {};
      }
    } else {
      constants.global_registry[rails_session_id] = {};
    }
    var tt = 'desktop';
    if(options.realMobile) {
      tt = 'realMobile';
    }
    var start_time_ts = helper.getDate();
    var autoWait = 10000;
    if(typeof browserstackParams['browserstack.autoWait'] !== 'undefined' && !isNaN(parseInt(browserstackParams['browserstack.autoWait'])))
      autoWait = parseInt(browserstackParams['browserstack.autoWait'])*1000;

    var webDriverLogsEnabled;
    if (browserstackParams['browserstack.seleniumLogs'] && browserstackParams['browserstack.seleniumLogs'].toString() === 'false') {
      webDriverLogsEnabled = false;
    } else {
      webDriverLogsEnabled = true;
    }

    var server_port = getServerPort(request);

    // KEYOBJECT-CREATION
    constants.global_registry[rails_session_id] = {
      seCdpUpstream: options.seCdpUpstream,
      seBidiUpstream: options.seBidiUpstream,
      name: hostname,
      hubHostName: request.headers.host,
      rproxyHost: options.rproxyHost,
      user: basic_auth[0],
      accesskey: basic_auth[1],
      debug: (browserstackParams['browserstack.debug'] == 'true' || browserstackParams['browserstack-debug'] == 'true'),
      syncTimeWithNTP: (browserstackParams['browserstack.syncTimeWithNTP'] || false).toString(),
      appTesting: bsCaps['app'] || false,
      key: sessionId,
      screenshot_counter: 1,
      debug_screenshot_counter: 0,
      rails_session_id: rails_session_id,
      s3key: browserstackParams['browserstack.aws.key'], s3secret: browserstackParams['browserstack.aws.secret'],
      s3bucket: data[0],
      port: options.port,
      os: bsCaps['orig_os'],
      os_passed_by_user: bsCaps['os'] || bsCaps['platform'],
      os_version: bsCaps['os_version'] || bsCaps['device'] || bsCaps['orig_os'],
      browser: bsCaps['browserName'] || bsCaps['browser'],
      browser_version: bsCaps['version'] || bsCaps['device'],
      build_number,
      deviceName: bsCaps['mobile'] ? bsCaps['mobile']['version'] : false,
      deviceOrientation: bsCaps['mobile'] ? (bsCaps['deviceOrientation'] || 'portrait').toString().toLowerCase() : false,
      client_ip: client_ip,
      delay_reg: true,
      timestamp: Date.now(),
      pendingDelete: false,
      device: device,
      idle_timeout: parseInt(browserstackParams['browserstack.idleTimeout'] || caps['browserstack.idleTimeout'], 10) * 1000 || constants.IDLE_TIMEOUT,
      tunnel: (browserstackParams['browserstack.tunnel'].toString() === 'true'),
      certs: ((bsCaps['acceptSslCerts'] || '').toString() === 'true'),
      secondary_state: constants.secondary_states.SUCCESS,
      is_snapshot: bsCaps['is_snapshot'] || 0,
      isSmartTV: isTrueString(bsCaps['isSmartTV']),
      debug_url: (bsCaps['checkURL'] || typeof bsCaps['checkURL'] === 'undefined') && (bsCaps['browserName'] || bsCaps['browser']) != 'MicrosoftEdge' && !options.realMobile,
      desktop_screenshots: (browserstackParams['browserstack.customScreenshots'] && browserstackParams['browserstack.customScreenshots'].toString().toLowerCase() == 'true'),
      autoitSendKeys: ( browserstackParams['browserstack.customSendKeys'] && browserstackParams['browserstack.customSendKeys'].toString() !== null && [ 'false', '0' ].indexOf(browserstackParams['browserstack.customSendKeys'].toString().toLowerCase()) < 0 ) ? browserstackParams['browserstack.customSendKeys'].toString() : null,
      jsSendKeys: Boolean( browserstackParams['browserstack.sendKeys'] && [ 'false', '0' ].indexOf(browserstackParams['browserstack.sendKeys'].toString().toLowerCase()) < 0 ) ,
      headless: Boolean( browserstackParams['browserstack.headless'] && [ 'false', '0' ].indexOf(browserstackParams['browserstack.headless'].toString().toLowerCase()) < 0) ,
      maskSendKeys: Boolean( browserstackParams['browserstack.maskSendKeys'] && [ 'false', '0' ].indexOf(browserstackParams['browserstack.maskSendKeys'].toString().toLowerCase()) < 0 ),
      maskResponse: Boolean( browserstackParams['browserstack.maskResponse'] && [ 'false', '0' ].indexOf(browserstackParams['browserstack.maskResponse'].toString().toLowerCase()) < 0 ),
      maskCommands: helper.getParsedObjectOrEmpty(browserstackParams['browserstack.maskCommands'], []),
      checkURL_url: '',
      realMobile: options.realMobile,
      terminal_type: tt,
      exceptionEncountered: false,
      isWebDriverIOSession: ( ( request.language || '').toLowerCase().match(/webdriverio/i) !== null ),
      isMetaPageLoadTimeout: false,
      lastRequestTime: Date.now(),
      lastResponseTime: Date.now(),
      outsideBrowserstackTime: 0,
      userHubLatency: null,
      seleniumRequestsCount: 0,
      hubTime: 0,
      insideHubTime: 0,
      hubProcessingTime: 0,
      userToNginxTime: 0,
      nginxToHubTime: 0,
      jarTime: 0,
      request_count: 0,
      take_screenshot_counter: 0,
      proxy_type: options['proxy_type'],
      url_changed: false,
      automation_session_id: options.automation_session_id,
      build_hash: options.build_hash,
      collection_number: options.collection_number,
      user_id: options.user_id,
      group_id: options.group_id,
      video: browserstackParams['browserstack.video'] && browserstackParams['browserstack.video'] == 'true',
      video_aws_keys: browserstackParams['browserstack.video.aws.key'],
      video_aws_secret: browserstackParams['browserstack.video.aws.secret'],
      video_aws_bucket: browserstackParams['browserstack.video.aws.s3bucket'],
      video_aws_region: browserstackParams['browserstack.video.aws.region'],
      video_aws_storage_class: browserstackParams['browserstack.video.aws.storageclass'],
      video_disable_watermark: browserstackParams['browserstack.video.disableWaterMark'],
      video_file: browserstackParams['browserstack.video.filename'] || 'video',
      video_params_v2: options.video_params_v2,
      logs_new_bucketing: isTrueString(bsCaps["new_bucketing"]),
      logs_aws_keys: browserstackParams['browserstack.logs.aws.key'],
      logs_aws_secret: browserstackParams['browserstack.logs.aws.secret'],
      logs_aws_bucket: browserstackParams['browserstack.logs.aws.s3bucket'],
      logs_aws_region: browserstackParams['browserstack.logs.aws.region'],
      logs_aws_storage_class: browserstackParams['browserstack.logs.aws.storageclass'],
      aws_new_user_enabled: constants.AWS_NEW_USER_ENABLED_ALL || ( constants.AWS_NEW_USER_ENABLED_GROUPS && options && options.group_id && ( constants.AWS_NEW_USER_ENABLED_GROUPS.includes(options.group_id) || constants.AWS_NEW_USER_ENABLED_GROUPS.includes(options.group_id.toString()) ) ),
      autoWait: autoWait,
      instable: false,
      start_time_ts: start_time_ts,
      originRegion: constants.region,
      noBlankPolling: Boolean( browserstackParams['browserstack.noBlankPolling'] && [ 'false', '0' ].indexOf(browserstackParams['browserstack.noBlankPolling'].toString().toLowerCase()) < 0 ),
      setPageLoadTimeout: ((typeof browserstackParams['browserstack.noPageLoadTimeout'] == 'undefined') || browserstackParams['browserstack.noPageLoadTimeout'].toString() == 'false'),
      session_start_time: Date.now(),
      appiumandroid: (options.bsCaps['mobile'] && options.bsCaps['mobile']['version'] && options.bsCaps['platform'] && options.browserName && (options.bsCaps['platform'].toLowerCase() == 'android' || options.browserName.toLowerCase() == 'android')) && Number(options.bsCaps['mobile']['version'].split('-')[1]) >= 4.4,
      IE11W10FileUpload: false,
      ieSpecialKeyPress: [],
      exceptionClass: null,
      exceptionRequest: null,
      check_url: null,
      logging: (browserstackParams['browserstack.logging'] ? browserstackParams['browserstack.logging'].toString() : false),
      maskBasicAuth: (browserstackParams['browserstack.maskBasicAuth'] ? browserstackParams['browserstack.maskBasicAuth'].toString() : 'false'),
      sleepTime: 0,
      numSleep: 0,
      uploaderKey: null,
      nonZeroIncrementCounters: [],
      udpKeys: {},
      captureCrash: browserstackParams['browserstack.captureCrash'] && browserstackParams['browserstack.captureCrash'] == 'true',
      safariPrivoxyTimeout: false,
      stopLogUploaded: false,
      consoleLogsEnabled: (browserstackParams['browserstack.console'] && browserstackParams['browserstack.console'].toString() !== 'disable'),
      lastOpenedUrl: null,
      firstOpenedUrl: null,
      qig: { sampleScriptUrl: true, sessionStatusExecUsed: false },
      isPuppeteer: isTrueString(bsCaps['isPuppeteer']),
      isPlaywright: isTrueString(bsCaps['isPlaywright']),
      isDetox: isTrueString(bsCaps['isDetox']),
      isLaunchPersistentContext: (browserstackParams["mediaFiles"] || isTrueString(browserstackParams["browserstack.accessibility"])) ? true : false,
      hub_to_term_error: false,
      clientDialect: options.clientDialect,
      dialect: options.dialect,
      wda_port: caps['wda_port'],
      webRTCStreamingEnabled: isTrueString(options.browserstackParams['browserstack.webrtcStreaming']),
      timeoutManagerIndex: helper.timeoutManagerGetIndex(rails_session_id),
      safari_allow_popups: ((bsCaps['safariAllowPopups']) ? (bsCaps['safariAllowPopups'].toString() === 'true') : false),
      selenium_version: bsCaps['selenium_version'] || browserstackParams['browserstack.selenium.jar.version'] || options.selenium_version,
      appium_version: browserstackParams['browserstack.appium_version'] || browserstackParams['browserstack.appiumVersion'],
      networkLogs: (browserstackParams['browserstack.networkLogs'] && browserstackParams['browserstack.networkLogs'].toString() === 'true'),
      webDriverLogs: webDriverLogsEnabled,
      appiumLogs: (browserstackParams['browserstack.appiumLogs'] && browserstackParams['browserstack.appiumLogs'].toString() === 'true'),
      deviceLogs: (browserstackParams['browserstack.deviceLogs'] && browserstackParams['browserstack.deviceLogs'].toString() === 'true') || false,
      hasBasicAuthPopup: false,
      video_start_time: options.video_start_time,
      platform_video_start_time: options.platform_video_start_time,
      customFullReset: (bsCaps.customFullReset && bsCaps.customFullReset.toString() === 'true'),
      newCommandTimeout: bsCaps.newCommandTimeout || 0,
      nonZeroStatusesCount: {},
      debugSession: (isTrueString(bsCaps['browserstack.debugSession'])) || (isTrueString(bsCaps['browserstack.debug_session'])),
      hoothootCanaryTags: options.hoothootCanaryTags,
      resignApp: helper.isDefined(browserstackParams['browserstack.resignApp']) ? browserstackParams['browserstack.resignApp'].toString().toLowerCase() === "true" : true,
      skipPlatformEnterpriseFlow: helper.isDefined(browserstackParams['skip_platform_enterprise_flow']) ? browserstackParams['skip_platform_enterprise_flow'].toString().toLowerCase() === "true" : false,
      enableBiometric: helper.isDefined(browserstackParams['browserstack.enableBiometric']) ? browserstackParams['browserstack.enableBiometric'].toString().toLowerCase() === "true" : false,
      enableCameraImageInjection: helper.isDefined(browserstackParams['browserstack.enableCameraImageInjection']) ? browserstackParams['browserstack.enableCameraImageInjection'].toString().toLowerCase() === "true" : false,
      enableCameraVideoInjection: helper.isDefined(browserstackParams['browserstack.enableCameraVideoInjection']) ? browserstackParams['browserstack.enableCameraVideoInjection'].toString().toLowerCase() === "true" : false,
      isSessionStartedUsingAppiumDesktop: (bsCaps['browserstack.source'] === 'appiumdesktop' || bsCaps.source === 'appiumdesktop'),
      isAppSettingsBundlePresent: helper.isDefined(browserstackParams['browserstack.isAppSettingsBundlePresent']) ? browserstackParams['browserstack.isAppSettingsBundlePresent'].toString().toLowerCase() === "true" : false,
      appDisplayName: helper.isDefined(browserstackParams['browserstack.appDisplayName']) ? browserstackParams['browserstack.appDisplayName'] : "",
      sessionStartedAt: helper.getFromConsoleTimes(request, 'received-request'),
      playwrightVersion: browserstackParams['browserstack.playwrightVersion'],
      instrumentBlackScreenshot: helper.isDefined(browserstackParams['browserstack.instrumentBlackScreenshot']) ? browserstackParams['browserstack.instrumentBlackScreenshot'].toString().toLowerCase() === "true" : false,
      sendDummyGetUrlRespToTestcafe: bsCaps['detected_language'] === 'testcafe-browserstack' && bsCaps['device'] && bsCaps['device'].toString().length !== 0,
      adbCustomExecutor: helper.isDefined(browserstackParams['browserstack.adbCustomExecutor']) ? browserstackParams['browserstack.adbCustomExecutor'].toString().toLowerCase() === "true" : false,
      translateLocalhostUrl: browserstackParams['translate_localhost_url'],
      clientConnectionSocketsCount: helper.isDefined(request.headers["x-connection-requests"]) ? parseInt(request.headers["x-connection-requests"]) : 0,
      automationName: bsCaps.automationName,
      bundleId: bsCaps.bundleId,
      markedAsPercy: false,
      percyBeginTime: -1,
      percyNumberOfTiles: 0,
      georestricted_region: browserstackParams['browserstack.georestricted_region'],
      aiEnabledSessions: helper.isDefined(browserstackParams['browserstack.ai_enabled_session']) ? browserstackParams['browserstack.ai_enabled_session'].toString().toLowerCase() === 'true' : false,
      aiSessionDetails: (helper.isDefined(browserstackParams['browserstack.ai_enabled_session']) && browserstackParams['browserstack.ai_enabled_session'].toString().toLowerCase() === 'true') ? JSON.parse(browserstackParams['browserstack.ai_details']) : {},
      selfHealingSuccess: false,
      softHealingSuccess: false,
      midSessionHealingDisabled: false,
      aiTcgDetails: JSON.stringify({
        region: constants.region,
        tcgUrls: constants.TCG_SERVICE.regions,
      }),
      ai_healing_details: {
        total_healing_enabled_request: 0,
        total_healing_request: 0,
        script_exec_error_count: 0,
        pre_check_failure_count: 0,
        healing_failure_count: 0,
        healing_success_count: 0
      },
      aiTcgHostname: AICommandHelper.getTcgEndpoint(),
      aiSessionTimestamp: Date.now(),
      automate_ai_duration: 0,
      automate_ai_success: 0,
      automate_ai_retry_count: 0,
      automate_ai_find_element_count: 0,
      automate_tcg_duration: 0,
      enableSim: helper.isDefined(browserstackParams['browserstack.enableSim']) ? browserstackParams['browserstack.enableSim'].toString().toLowerCase() === 'true' : false,
      enableApplePay: isTrueString(browserstackParams['browserstack.enableApplePay']),
      cameraInjection: helper.isDefined(browserstackParams['browserstack.cameraInjection']) ? browserstackParams['browserstack.cameraInjection'].toString().toLowerCase() === 'true' : false,
      cameraInjectionUrl: browserstackParams['browserstack.cameraInjectionUrl'],
      simPhoneNumber: bsCaps['phoneNumber'],
      simRegion: bsCaps['simRegion'],
      is_dedicated_cloud_session: isTrueString(browserstackParams['browserstack.is_dedicated_cloud_session']),
      aaDeviceDateAccessible: browserstackParams['aaDeviceDateAccessible'],
      optimalHubUrl: options.optimalHubUrl ? options.optimalHubUrl : '',
      terminalRegion: browserstackParams["browserstack.terminal_region"],
      terminalSubRegion: browserstackParams["browserstack.terminal_sub_region"],
      extendedSessionDuration: browserstackParams["browserstack.extendedSessionDuration"] || false,
      esim: bsCaps['esim'],
      enableAudioInjection: helper.isDefined(browserstackParams['browserstack.enableAudioInjection']) ? browserstackParams['browserstack.enableAudioInjection'].toString().toLowerCase() === 'true' : false,
      mockPerformanceJarEndpoint: helper.isDefined(browserstackParams['browserstack.mock_performance_jar_endpoint']) ? browserstackParams['browserstack.mock_performance_jar_endpoint'].toString().toLowerCase() === 'true' : false,
      userPassedLocalCap: helper.isDefined(browserstackParams['user_passed_local_cap']) ? browserstackParams['user_passed_local_cap'].toString().toLowerCase() === 'true' : false,
      platformDetails: options.platformDetails,
      enableHubLogsZipping: helper.isDefined(browserstackParams['enable_zipped_logs']) ? browserstackParams['enable_zipped_logs'].toString().toLowerCase() === 'true' : false,
      accessibleFeaturesList: browserstackParams['accessible_features_list'],
      bundlesForMultiAppImageInjection: browserstackParams['bundles_for_multi_app_image_injection'],
      lighthouseAutomate: { report_limit: browserstackParams['browserstack.report_limit'], assert_limit: browserstackParams['browserstack.assert_limit'] },
      elementNotFound: false,
      pageLoadError: false,
      server_port: server_port,
      skip_screenshot_upload: helper.isDefined(browserstackParams['browserstack.maskCommands']) ? browserstackParams['browserstack.maskCommands'].indexOf('screenshot') > -1 : false,
    };

    if(constants.instrumentationMechanismFlag !== 2){
      helper.sendDataToInstrumentationService(constants.global_registry[rails_session_id],'REQUEST_START',constants.global_registry[rails_session_id]);
    }
    helper.takeScreenshotAndUpload(constants.global_registry[rails_session_id], 1);
    constants.lastLogTime = new Date();
    constants.sessions_registry[rails_session_id] = hostname;

    sessionManagerHelper.recreateRegistry(constants.global_registry[rails_session_id]);

    ha.setData(rails_session_id, constants.global_registry[rails_session_id], () => {
      if (callback) {
        callback();
      }
    });
  }
  return constants.global_registry[rails_session_id];
}

exports.addNewMappingInRegistry = addNewMappingInRegistry;

function regSession(rails_session_id, hostname, selenium_key, start_time_ts, output_caps, isAppAutomate, regSessionOptions) {
  var customFieldHash = {
    browsermob_failed: regSessionOptions.recoveredBrowserMobError || false,
    selenium_version: regSessionOptions.selenium_version || '',
    forceChangeJar: regSessionOptions.shouldForceChangeJar || false
  };
  let keyObject = constants.global_registry[rails_session_id];
  if (keyObject) {
    if (keyObject.video_start_time) {
      customFieldHash.video_start_time = keyObject.video_start_time;
      customFieldHash.platform_video_start_time = keyObject.platform_video_start_time;
    }
    customFieldHash.selenium_version = keyObject.selenium_version;
  }


  var regSessionHash = {
    ci: hostname,
    k: rails_session_id,
    reg_session: true,
    selenium_key: selenium_key,
    start_time_ts: start_time_ts,
  };

  // Adds the window build number in the session hash parameter
  if (regSessionOptions.build_number) {
    regSessionHash['build_number'] = regSessionOptions.build_number;
  }

  // custom_field_hash is stored in 'custom_field' column in automation_session table
  browserstack.postBrowserStack('&' + requestlib.getEncodedURLParams(regSessionHash), {
    output_caps: output_caps,
    isAppAutomate: isAppAutomate,
    custom_field_hash: customFieldHash,
  });
}
exports.regSession = regSession;

function handleRequest401(request, response, keyIdentifier, subKeyIdentifier = '', userNameForLogging = null) {
  response.writeHead(401, {'content-type': 'application/text; charset=utf-8', 'accept': 'application/text', 'WWW-Authenticate': 'Basic realm="BrowserStack Selenium Hub"'});
  HubLogger.instrumentationStats(keyIdentifier, {user: userNameForLogging}, subKeyIdentifier, 'Authorization required');
  helper.respondWithError(request, response, 'Authorization required');
}
exports.handleRequest401 = handleRequest401;

function getClientAddress(req) {
  return req.headers['x-forwarded-for'] || req.connection.remoteAddress;
}
exports.getClientAddress = getClientAddress;

function getUserAgent(request) {
  return request.headers['user-agent'] || request.headers['User-Agent'] || request.headers['User Agent'];
}
exports.getUserAgent = getUserAgent;

function getServerPort(request) {
  return request.headers['X-Server-Port'] || request.headers['x-server-port'];
}
exports.getServerPort = getServerPort;

function replaceSessionID(data, thisThing, withThisThing, parsed_data) {
  var return_val = data? Buffer.from(data.replace(new RegExp(thisThing, 'g'), withThisThing), 'utf-8') : '';
  if(!parsed_data)
    return return_val;
  if(parsed_data.value && parsed_data.value.length > 1){
    parsed_data.value = [parsed_data.value.join('')];
    var modified_temp_data = Buffer.from(JSON.stringify(parsed_data).replace(new RegExp(thisThing, 'g'), withThisThing), 'utf-8');
    if(parsed_data.value[0].length !== JSON.parse(modified_temp_data).value[0].length)
      return_val = modified_temp_data;
  }
  return return_val;
}
exports.replaceSessionID = replaceSessionID;

function sessionNotFound(response, host_params, subKeyIdentifier='') {
  response.writeHead('404', {'content-type': 'application/json; charset=utf-8', 'accept': 'application/json'});

  if(host_params == false) {
    //This means that URL does not have session id. Send 404 response since handling some tools like TOSCA do not send /wd/hub/status for 422 response codes
    let dataToSend = JSON.stringify({value: {message: 'Invalid Command'}});
    HubLogger.instrumentationStats('Missing Session ID', {}, subKeyIdentifier, dataToSend);
    response.end(dataToSend);
  } else {
    // In this case host_params is undefined that means there was session in the url but in the registry
    let dataToSend = JSON.stringify({value: {message: 'Session not started or terminated'}, sessionId: '', 'status': 13});
    HubLogger.instrumentationStats('Session Not Found', host_params, subKeyIdentifier, dataToSend);
    response.end(dataToSend);

    if (host_params && host_params.rails_session_id) {
      HubLogger.miscLogger('SESSION_NOT_FOUND', 'Session not started or terminated for: ' + host_params.rails_session_id, LL.WARN);
    }
  }
}
exports.sessionNotFound = sessionNotFound;

function blocked(request, response) {
  var client_ip = getClientAddress(request);
  if(constants.blockedIPs.indexOf(client_ip) > -1 ||
    (constants.blockIPRegex !='' && client_ip.match(new RegExp(constants.blockIPRegex, 'g')))
    ) {
    HubLogger.miscLogger('SPAMMER', 'Blocking IP: ' + client_ip, LL.DEBUG);
    setTimeout(function(){
      handleRequest401(request, response, 'Blocked', 'Spammer IP');
    }, constants.blockedUserTimeout);
    return true;
  }
  return false;
}

function blockedUser(request, response, username) {
  if(constants.blockedUsers.indexOf(username) > -1) {
    HubLogger.miscLogger('SPAMMER', 'Blocking User: ' + username, LL.DEBUG);
    setTimeout(function(){
      handleRequest401(request, response, 'Blocked', 'Spam User');
    }, constants.blockedUserTimeout);
    return true;
  }

  return false;
}
