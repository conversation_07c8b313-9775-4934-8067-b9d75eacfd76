'use strict';

const { <PERSON><PERSON><PERSON>og<PERSON>and<PERSON> } = require('../../../../../controllers/seleniumCommand/handlers/DeviceLogHandler');
const bridge = require('../../../../../bridge');
const requestlib = require('../../../../../lib/request');
const HubLogger = require('../../../../../log');
const constants = require('../../../../../constants');
const ha = require('../../../../../ha');
const pubSub = require('../../../../../pubSub');
const sinon = require('sinon');
const { assert } = require('chai');

describe('DeviceLogHandler tests', () => {
  let sandbox;

  beforeEach(() => {
    sandbox = sinon.sandbox.create();
  });

  afterEach(() => {
    sandbox.restore();
  });

  function createStubs(options = {}) {
    const stubs = {
      sendResponse: sandbox.stub(bridge, 'sendResponse', options.sendResponse || (() => {})),
      requestCall: sandbox.stub(requestlib, 'call', options.requestCall || (() => Promise.resolve({}))),
      appendBStackHostHeader: sandbox.stub(requestlib, 'appendBStackHostHeader', options.appendBStackHostHeader || (() => ({ Host: 'test-terminal' }))),
      miscLogger: sandbox.stub(HubLogger, 'miscLogger', options.miscLogger || (() => {})),
      exceptionLogger: sandbox.stub(HubLogger, 'exceptionLogger', options.exceptionLogger || (() => {})),
      setData: sandbox.stub(ha, 'setData', options.setData || (() => {})),
      publish: sandbox.stub(pubSub, 'publish', options.publish || (() => {})),
    };

    return stubs;
  }

  describe('constructor', () => {
    it('should initialize with sessionKeyObj, request, and response', () => {
      const sessionKeyObj = { rails_session_id: 'test-session' };
      const request = {};
      const response = {};
      const handler = new DeviceLogHandler(sessionKeyObj, request, response);
      assert.strictEqual(handler.sessionKeyObj, sessionKeyObj);
      assert.strictEqual(handler.request, request);
      assert.strictEqual(handler.response, response);
    });
  });

  describe('processCommand', () => {
    let mockSessionKeyObj;
    let mockRequestStateObj;

    beforeEach(() => {
      mockSessionKeyObj = {
        rails_session_id: 'test-session-123',
        device: 'test-device',
        deviceLogs: 'true',
        idle_timeout: 30,
        rproxyHost: 'test-host',
        name: 'test-terminal',
        debugSession: false,
      };

      mockRequestStateObj = {
        data: null,
        output: null,
      };
    });

    it('should successfully process logcat request and return logs', async () => {
      const platformResponse = {
        statusCode: 200,
        headers: { 'x-end-pos': '1000' },
        data: JSON.stringify([
          { timestamp: 1748599327715, level: 'INFO', message: 'Test log entry' },
        ]),
      };

      const stubs = createStubs({
        sendResponse: (sessionKeyObj, requestStateObj) => {
          const responseData = JSON.parse(requestStateObj.data);
          assert.strictEqual(responseData.sessionId, 'test-session-123');
          assert.strictEqual(responseData.status, 0);
          assert.isArray(responseData.value);
          assert.strictEqual(responseData.value.length, 1);
          assert.strictEqual(responseData.value[0].message, 'Test log entry');
        },
        requestCall: () => Promise.resolve(platformResponse),
      });
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });
      await handler.processCommand(mockRequestStateObj, logData);
      sinon.assert.calledOnce(stubs.requestCall);
      const callArgs = stubs.requestCall.getCall(0).args[0];
      assert.strictEqual(callArgs.hostname, 'test-host');
      assert.strictEqual(callArgs.port, 45671);
      assert.include(callArgs.path, '/device_logs');
      assert.include(callArgs.path, 'device=test-device');
      assert.include(callArgs.path, 'session_id=test-session-123');
      assert.include(callArgs.path, 'log_type=logcat');
      assert.include(callArgs.path, 'start_pos=0');
      // Verify response was sent
      sinon.assert.calledOnce(stubs.sendResponse);
      // Verify persistence methods were called
      sinon.assert.calledOnce(stubs.setData);
      sinon.assert.calledOnce(stubs.publish);
    });

    it('should successfully process syslog request and return logs', async () => {
      const platformResponse = {
        statusCode: 200,
        headers: { 'x-end-pos': '500' },
        data: JSON.stringify([
          { timestamp: 1748599327715, level: 'ERROR', message: 'System error log' },
        ]),
      };

      const stubs = createStubs({
        sendResponse: (sessionKeyObj, requestStateObj) => {
          const responseData = JSON.parse(requestStateObj.data);
          assert.strictEqual(responseData.status, 0);
          assert.strictEqual(responseData.value[0].message, 'System error log');
        },
        requestCall: () => Promise.resolve(platformResponse),
      });

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'syslog' });
      await handler.processCommand(mockRequestStateObj, logData);
      // Verify platform request was made with syslog type
      sinon.assert.calledOnce(stubs.requestCall);
      const callArgs = stubs.requestCall.getCall(0).args[0];
      assert.include(callArgs.path, 'log_type=syslog');
      // Verify response was sent
      sinon.assert.calledOnce(stubs.sendResponse);
    });

    it('should handle pagination by using deviceLogEndPos from session', async () => {
      mockSessionKeyObj.deviceLogEndPos = 1500;
      const platformResponse = {
        statusCode: 200,
        headers: { 'x-end-pos': '2000' },
        data: JSON.stringify([]),
      };
      const stubs = createStubs({
        requestCall: () => Promise.resolve(platformResponse),
      });
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });
      await handler.processCommand(mockRequestStateObj, logData);
      // Verify start_pos was set from deviceLogEndPos
      const callArgs = stubs.requestCall.getCall(0).args[0];
      assert.include(callArgs.path, 'start_pos=1500');
      // Verify deviceLogEndPos was updated
      assert.strictEqual(mockSessionKeyObj.deviceLogEndPos, 2000);
      // Verify persistence methods were called
      sinon.assert.calledOnce(stubs.setData);
      sinon.assert.calledOnce(stubs.publish);
    });

    it('should update global_registry with new deviceLogEndPos', async () => {
      const sessionId = 'test-session-123';
      constants.global_registry[sessionId] = {
        ...mockSessionKeyObj,
        deviceLogEndPos: 0,
      };
      const platformResponse = {
        statusCode: 200,
        headers: { 'x-end-pos': '3000' },
        data: JSON.stringify([]),
      };
      const stubs = createStubs({
        requestCall: () => Promise.resolve(platformResponse),
      });
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });
      await handler.processCommand(mockRequestStateObj, logData);
      // Verify global_registry was updated
      assert.strictEqual(constants.global_registry[sessionId].deviceLogEndPos, 3000);
    });

    it('should use latest session object from global_registry if available', async () => {
      const sessionId = 'test-session-123';
      const latestSessionObj = {
        ...mockSessionKeyObj,
        deviceLogEndPos: 999,
        device: 'updated-device',
      };
      constants.global_registry[sessionId] = latestSessionObj;
      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 999, end_pos: 1200 },
          value: [],
        }),
      };
      const stubs = createStubs({
        requestCall: () => Promise.resolve(platformResponse),
      });
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });
      await handler.processCommand(mockRequestStateObj, logData);
      // Verify updated session object was used
      const callArgs = stubs.requestCall.getCall(0).args[0];
      assert.include(callArgs.path, 'device=updated-device');
      assert.include(callArgs.path, 'start_pos=999');
    });

    it('should return error when device logs are not enabled', async () => {
      mockSessionKeyObj.deviceLogs = 'false';
      const stubs = createStubs({
        sendResponse: (sessionKeyObj, requestStateObj) => {
          const responseData = JSON.parse(requestStateObj.data);
          assert.strictEqual(responseData.status, 13);
          assert.strictEqual(requestStateObj.output.statusCode, 500);
        },
      });
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });
      await handler.processCommand(mockRequestStateObj, logData);
      // Verify error response was sent
      sinon.assert.calledOnce(stubs.sendResponse);
    });

    it('should allow device logs when enabled as string true', async () => {
      mockSessionKeyObj.deviceLogs = 'true';
      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 0, end_pos: 100 },
          value: [],
        }),
      };
      const stubs = createStubs({
        sendResponse: (sessionKeyObj, requestStateObj) => {
          const responseData = JSON.parse(requestStateObj.data);
          assert.strictEqual(responseData.status, 0);
        },
        requestCall: () => Promise.resolve(platformResponse),
      });
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });
      await handler.processCommand(mockRequestStateObj, logData);
      // Verify platform request was made
      sinon.assert.calledOnce(stubs.requestCall);
      sinon.assert.calledOnce(stubs.sendResponse);
    });

    it('should return error for invalid JSON in request data', async () => {
      const stubs = createStubs({
        sendResponse: (sessionKeyObj, requestStateObj) => {
          const responseData = JSON.parse(requestStateObj.data);
          assert.strictEqual(responseData.status, 13);
          assert.include(responseData.value.message, '[BROWSERSTACK_INTERNAL_ERROR]');
          assert.strictEqual(requestStateObj.output.statusCode, 500);
        },
      });
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const invalidJsonData = '{"type": "logcat"'; // Missing closing brace
      await handler.processCommand(mockRequestStateObj, invalidJsonData);
      // Verify no platform request was made
      sinon.assert.notCalled(stubs.requestCall);
      // Verify error response was sent
      sinon.assert.calledOnce(stubs.sendResponse);
    });

    it('should return error when platform returns non-200 status code', async () => {
      const platformResponse = {
        statusCode: 500,
        data: 'Internal Server Error',
      };
      const stubs = createStubs({
        sendResponse: (sessionKeyObj, requestStateObj) => {
          const responseData = JSON.parse(requestStateObj.data);
          assert.strictEqual(responseData.status, 13);
          assert.include(responseData.value.message, '[BROWSERSTACK_INTERNAL_ERROR]');
          assert.include(responseData.value.message, 'We couldn\'t retrieve device logs');
          assert.strictEqual(requestStateObj.output.statusCode, 500);
        },
        requestCall: () => Promise.resolve(platformResponse),
      });
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });
      await handler.processCommand(mockRequestStateObj, logData);
      // Verify platform request was made
      sinon.assert.calledOnce(stubs.requestCall);
      // Verify error response was sent
      sinon.assert.calledOnce(stubs.sendResponse);
    });

    it('should return error when platform returns invalid JSON', async () => {
      const platformResponse = {
        statusCode: 200,
        data: 'invalid json response',
      };
      const stubs = createStubs({
        sendResponse: (sessionKeyObj, requestStateObj) => {
          const responseData = JSON.parse(requestStateObj.data);
          assert.strictEqual(responseData.status, 13);
          assert.include(responseData.value.message, '[BROWSERSTACK_INTERNAL_ERROR]');
          assert.strictEqual(requestStateObj.output.statusCode, 500);
        },
        requestCall: () => Promise.resolve(platformResponse),
      });
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });
      await handler.processCommand(mockRequestStateObj, logData);
      // Verify platform request was made
      sinon.assert.calledOnce(stubs.requestCall);
    });

    it('should handle platform request exception', async () => {
      const stubs = createStubs({
        sendResponse: (sessionKeyObj, requestStateObj) => {
          const responseData = JSON.parse(requestStateObj.data);
          assert.strictEqual(responseData.status, 13);
          assert.include(responseData.value.message, '[BROWSERSTACK_INTERNAL_ERROR]');
          assert.strictEqual(requestStateObj.output.statusCode, 500);
        },
        requestCall: () => Promise.reject(new Error('Network error')),
      });
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });
      await handler.processCommand(mockRequestStateObj, logData);
      // Verify platform request was attempted
      sinon.assert.calledOnce(stubs.requestCall);
      // Verify error response was sent
      sinon.assert.calledOnce(stubs.sendResponse);
      // Verify exception was logged
      sinon.assert.calledOnce(stubs.exceptionLogger);
    });

    it('should properly encode URL parameters', async () => {
      mockSessionKeyObj.device = 'Samsung Galaxy S21+';
      mockSessionKeyObj.rails_session_id = 'session-with-special-chars@#$';
      const platformResponse = {
        statusCode: 200,
        headers: { 'x-end-pos': '100' },
        data: JSON.stringify([]),
      };
      const stubs = createStubs({
        requestCall: () => Promise.resolve(platformResponse),
      });
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });
      await handler.processCommand(mockRequestStateObj, logData);
      // Verify URL encoding was applied
      const callArgs = stubs.requestCall.getCall(0).args[0];
      assert.include(callArgs.path, 'device=Samsung%20Galaxy%20S21%2B');
      assert.include(callArgs.path, 'session_id=session-with-special-chars%40%23%24');
    });

    it('should handle platform response with missing value property', async () => {
      const platformResponse = {
        statusCode: 200,
        headers: { 'x-end-pos': '100' },
        data: JSON.stringify([]),
      };
      const stubs = createStubs({
        sendResponse: (sessionKeyObj, requestStateObj) => {
          const responseData = JSON.parse(requestStateObj.data);
          assert.strictEqual(responseData.status, 0);
          assert.isArray(responseData.value);
          assert.strictEqual(responseData.value.length, 0);
        },
        requestCall: () => Promise.resolve(platformResponse),
      });
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });
      await handler.processCommand(mockRequestStateObj, logData);
      // Verify response was sent with empty array
      sinon.assert.calledOnce(stubs.sendResponse);
    });

    it('should handle missing end_pos in platform response meta', async () => {
      const platformResponse = {
        statusCode: 200,
        // Missing x-end-pos header
        data: JSON.stringify([
          { timestamp: 1748599327715, level: 'INFO', message: 'Test log' },
        ]),
      };
      const stubs = createStubs({
        sendResponse: (sessionKeyObj, requestStateObj) => {
          const responseData = JSON.parse(requestStateObj.data);
          assert.strictEqual(responseData.status, 0);
        },
        requestCall: () => Promise.resolve(platformResponse),
      });
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });
      await handler.processCommand(mockRequestStateObj, logData);
      // Verify response was sent successfully
      sinon.assert.calledOnce(stubs.sendResponse);
      // Verify deviceLogEndPos was not updated
      assert.isUndefined(mockSessionKeyObj.deviceLogEndPos);
    });

    it('should verify request headers are properly set', async () => {
      const platformResponse = {
        statusCode: 200,
        headers: { 'x-end-pos': '100' },
        data: JSON.stringify([]),
      };
      const stubs = createStubs({
        requestCall: () => Promise.resolve(platformResponse),
      });
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });
      await handler.processCommand(mockRequestStateObj, logData);
      const callArgs = stubs.requestCall.getCall(0).args[0];
      assert.strictEqual(callArgs.hostname, 'test-host');
      assert.strictEqual(callArgs.port, 45671);
    });
  });
});
